[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:项目文件现代化 DESCRIPTION:将传统.csproj格式转换为SDK风格的项目文件，更新目标框架到.NET 8

请在所以的代码编写阶段用context7
-[x] NAME:依赖包更新 DESCRIPTION:更新所有NuGet包到.NET 8兼容版本，移除不兼容的包
-[x] NAME:WPF跨平台替代方案评估 DESCRIPTION:评估Avalonia UI、MAUI等跨平台UI框架，选择最适合的替代方案
--[x] NAME:Avalonia UI项目初始化 DESCRIPTION:创建Avalonia项目模板，配置基础依赖包和项目结构
--[x] NAME:主窗口XAML迁移 DESCRIPTION:将MainWindow.xaml从WPF格式迁移到Avalonia格式
--[x] NAME:自定义控件迁移 DESCRIPTION:迁移LogCurveControl和LogCurveImgControl等自定义控件
--[ ] NAME:数据绑定和事件处理适配 DESCRIPTION:调整数据绑定语法和事件处理代码以适配Avalonia
--[ ] NAME:依赖服务适配 DESCRIPTION:适配串口通信、文件操作、网络通信等服务到跨平台实现
-[ ] NAME:代码兼容性修复 DESCRIPTION:修复.NET Framework到.NET 8的API变更和兼容性问题
-[ ] NAME:配置和资源文件适配 DESCRIPTION:更新App.config、资源文件等配置到.NET 8格式
-[ ] NAME:构建和测试验证 DESCRIPTION:确保项目能在macOS上正常构建和运行