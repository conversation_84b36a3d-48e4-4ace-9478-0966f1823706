﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Media;
using Avalonia.Threading;
using Avalonia.Interactivity;
using System.Xml.Linq;
using TartanLogApp.DataSource;
using TartanLogApp.Models;

namespace TartanLogApp
{
    /// <summary>
    /// DataTCPTransWindow.xaml 的交互逻辑
    /// </summary>
    public partial class NetListenWindow : Window
    {
        private MeztlDB meztlDB;
        private List<LoggingData> loggingDatas;
        private StreamWriter logStream;

        public NetListenWindow(long jobID)
        {
            InitializeComponent();
            meztlDB = new MeztlDB(jobID);
            loggingDatas = new List<LoggingData>();
            string path = "logs_" + DateTime.Now.Ticks.ToString() + ".txt";
            this.logStream = new StreamWriter(path, false);
        }

        private NetListenerSevice connect;
        private Encoding encoding;
        private CancellationTokenSource m_cts;

        private void btnConnect_Click(object sender, RoutedEventArgs e)
        {
            string ports = tbPort.Text.Trim();
            m_cts = new CancellationTokenSource();
            if (!string.IsNullOrEmpty(ports))
            {
                try
                {
                    int port = Convert.ToInt32(ports);
                    if (rbTCP.IsChecked == true)
                    {
                        connect = new NetListenerSevice(ConnectType.TCP, port);
                    }
                    else
                    {
                        connect = new NetListenerSevice(ConnectType.UDP, port);
                    }
                    connect.OnRecvEvent += Recv;
                    connect.StartListen();
                    
                    Thread.Sleep(100);
                    
                    if (connect.IsStartListening)
                    {
                        lblStatus.Fill = Brush.Parse("Lime");
                        btnConnect.IsEnabled = false;

                        switch (cbCharset.SelectedIndex)
                        {
                            case 0:
                                encoding = Encoding.UTF8;
                                break;
                            case 1:
                                encoding = Encoding.Unicode;
                                break;
                            case 2:
                                encoding = Encoding.ASCII;
                                break;
                            case 3:
                                encoding = Encoding.GetEncoding("GB2312");
                                break;
                            default:
                                encoding = Encoding.UTF8;
                                break;
                        }

                        cbCharset.IsEnabled = false;

                    }
                    else
                    {
                        MessageBox.Show("开启服务失败！");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                    return ;
                }               

            }

            
        }

        private void Recv(byte[] data, int len)
        {
            if (!Dispatcher.CheckAccess())
            {
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    Recv(data, len);
                }));

                return;
            }

            if (len > 0) 
            {
                if (m_cts.Token.IsCancellationRequested)
                {
                    return;
                }

                string text = encoding.GetString(data.Take(len).ToArray());
                
                Dispatcher.Invoke(new Action(() =>
                {
                    ProcRecvData(text);
                    tbSend.Text = DateTime.Now.ToString() + "\n" + text + "\n";
                }));
                
            }

        }

        private void UpdateMezDepth(LoggingData logging)
        {
            string s1;
            int num = meztlDB.UpdateMezDepth(logging, out s1);            
            if (num == 1)
            {
                Dispatcher.Invoke(delegate
                {
                    this.lblInfo.Content = s1;
                    this.logStream.WriteLine(s1);
                    this.logStream.Flush();
                });
            }
        }

        private void ProcRecvData(string str)
        {
            string[] strings = str.Split('\n');
            if (strings.Length>0)
            {
                LoggingData logging = new LoggingData();
                for (int i = 0; i < strings.Length; i++)
                {
                    int len = strings[i].Length;
                    if (len > 4)
                    {
                        if (strings[i].Substring(0, 4).Equals("0106"))
                            logging.dateTime = DateTime.Parse(strings[i].Substring(4, len - 4));
                        if (strings[i].Substring(0, 4).Equals("0108"))
                            logging.BitDepth = double.Parse(strings[i].Substring(4, len - 4));
                        if (strings[i].Substring(0, 4).Equals("0110"))
                            logging.Depth = double.Parse(strings[i].Substring(4, len - 4));
                    }
                }

                if (logging.dateTime == DateTime.MinValue || logging.BitDepth < 0.1)
                {
                    return;
                }

                loggingDatas.Add(logging);

                if (logging.BitDepth > LoggingData.LastBitDepth)
                {
                    //更新mezintal 数据库
                    UpdateMezDepth(logging);
                    LoggingData.LastBitDepth = logging.BitDepth;
                }
                
                var dataItem = new
                {
                    Timestamp = logging.dateTime.ToString(),
                    Depth = logging.Depth.ToString("0.00"),
                    BitDepth = logging.BitDepth.ToString("0.00")
                };
                viewItem.HorizontalContentAlignment = HorizontalAlignment.Center;
                lstRecvData.Items.Add(dataItem);
                lstRecvData.SelectedIndex = lstRecvData.Items.Count - 1;
                lstRecvData.ScrollIntoView(lstRecvData.SelectedItem);
            }
        }

        private void btnDisConnect_Click(object sender, RoutedEventArgs e)
        {
            m_cts?.Cancel();
            connect?.Stop();
            lblStatus.Fill = Brush.Parse("Red");
            btnConnect.IsEnabled = true;
            cbCharset.IsEnabled = true;
        }

        private void Window_Closed(object sender, EventArgs e)
        {
            logStream.Flush();
            logStream.Close();
            this.Tag = "close";
        }

        private void btnClear_Click(object sender, RoutedEventArgs e)
        {
            tbSend.Text = "";
            lstRecvData.Items.Clear();
            loggingDatas.Clear();
        }
    }

    public class LoggingData
    {
        public DateTime dateTime;
        public double Depth;
        public double BitDepth;
        public static double LastBitDepth;
        public LoggingData()
        {
            this.dateTime = DateTime.MinValue;
            this.Depth = 0.0;
            this.BitDepth = 0.0;
            LastBitDepth = 0;
        }
        public override string ToString()
        {
            return string.Format("{0}\t{1:N2}\t{2:N2}", this.dateTime.ToString("G"), this.Depth, this.BitDepth);
        }
    }
}
