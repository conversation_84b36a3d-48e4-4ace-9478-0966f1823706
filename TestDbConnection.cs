using System;
using System.Data.SqlClient;

class TestDbConnection
{
    static void Main()
    {
        string connectionString = "Data Source=192.168.31.12;Initial Catalog=MEZINTELMWD;User ID=sa;Password=**********;TrustServerCertificate=true;Connection Timeout=10;";
        
        Console.WriteLine("测试数据库连接...");
        Console.WriteLine($"连接字符串: {connectionString.Replace("Password=**********", "Password=***")}");
        Console.WriteLine();
        
        try
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                Console.WriteLine("正在尝试连接数据库...");
                connection.Open();
                Console.WriteLine("✅ 数据库连接成功！");
                
                // 测试基本查询
                string testQuery = "SELECT @@VERSION as ServerVersion, DB_NAME() as DatabaseName, GETDATE() as CurrentTime";
                using (SqlCommand command = new SqlCommand(testQuery, connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Console.WriteLine($"📊 服务器版本: {reader["ServerVersion"]}");
                            Console.WriteLine($"📊 数据库名: {reader["DatabaseName"]}");
                            Console.WriteLine($"📊 服务器时间: {reader["CurrentTime"]}");
                        }
                    }
                }
                
                Console.WriteLine();
                
                // 测试表是否存在
                string tableQuery = @"
                    SELECT TABLE_NAME 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_TYPE = 'BASE TABLE' 
                    AND TABLE_NAME IN ('LOG_Job', 'LOG_JobHdrParams', 'LOG_BitRuns', 'vw_Results')
                    ORDER BY TABLE_NAME";
                    
                using (SqlCommand command = new SqlCommand(tableQuery, connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        Console.WriteLine("📋 检查关键表是否存在:");
                        bool hasData = false;
                        while (reader.Read())
                        {
                            Console.WriteLine($"  ✅ {reader["TABLE_NAME"]}");
                            hasData = true;
                        }
                        if (!hasData)
                        {
                            Console.WriteLine("  ⚠️  未找到预期的表");
                        }
                    }
                }
                
                connection.Close();
                Console.WriteLine("🔌 数据库连接已关闭");
            }
        }
        catch (SqlException sqlEx)
        {
            Console.WriteLine($"❌ SQL Server 错误: {sqlEx.Message}");
            Console.WriteLine($"   错误号: {sqlEx.Number}");
            Console.WriteLine($"   严重级别: {sqlEx.Class}");
            Console.WriteLine($"   状态: {sqlEx.State}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 连接失败: {ex.Message}");
            Console.WriteLine($"   异常类型: {ex.GetType().Name}");
        }
        
        Console.WriteLine();
        Console.WriteLine("测试完成。");
    }
}
