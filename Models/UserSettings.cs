﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.XPath;
// using TartanLogApp.CurveControl; // 暂时注释掉用于最小化验证

namespace TartanLogApp.Models
{
    public class UserSettings
    {
        private static UserSettings settings;
        private static readonly object lockobj = new object();
        public static UserSettings Instance
        {
            get
            {
                if (settings == null)
                {
                    lock (lockobj)
                    {
                        settings = new UserSettings();
                    }
                }
                return settings;
            }
        }

        public UserSettings() 
        { 

        }

        public static void SaveXML(string filepath)
        {
            XmlTextWriter xmlTextWriter = null;
            XmlDocument doc = ToXML();
            try
            {
                xmlTextWriter = new XmlTextWriter(filepath, Encoding.UTF8);
                xmlTextWriter.Formatting = Formatting.Indented;
                doc.Save(xmlTextWriter);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(ex.Message);
                xmlTextWriter?.Close();                
            }
        }

        public static void LoadXML(string filepath)
        {
            try
            {
                XmlDocument doc = new XmlDocument();                
                doc.Load(filepath);
                settings = FromXML(doc);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(ex.Message);                
                SaveXML(filepath);
            }
        }

        private static UserSettings FromXML(XmlDocument xml)
        {
            UserSettings s = new UserSettings();
            
            XPathNavigator xpathNavigator = xml.CreateNavigator();
            XPathNodeIterator xpathNodeIterator = (XPathNodeIterator)xpathNavigator.Evaluate("//AppSettingsData");
            xpathNodeIterator.MoveNext();
            XPathNavigator xpathNavigator2 = xpathNodeIterator.Current;
            s.AppVersion = Util.GetFirstXmlNodeValue(xpathNavigator2, "AppVersion");
            s.JobID = long.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "JobID"));
            s.Wellname = Util.GetFirstXmlNodeValue(xpathNavigator2, "WellName");
            s.survey = bool.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "Survey"));
            s.Interval = int.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "Interval"));

            s.smooth = bool.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "GammaCurve/CurveSmooth"));
            string grs = Util.GetFirstXmlNodeValue(xpathNavigator2, "GammaCurve/GRList");
            s.GRList = grs.Split(',').ToList();
            s.CurveCount = int.Parse(Util.GetXmlNodeAttribute(xpathNavigator2, "GammaCurve", "Count"));
            s.sector = int.Parse(Util.GetXmlNodeAttribute(xpathNavigator2, "GammaImging", "Sector"));
           
            if (s.sector > 0)
            {
                s.GammaImging = true;
                xpathNodeIterator = (XPathNodeIterator)xpathNavigator2.Evaluate("GammaImging/AzmItem");                
                while (xpathNodeIterator.MoveNext())
                {
                    XPathNavigator xPath = xpathNodeIterator.Current;
                    string[] temp = xPath.GetAttribute("Value", "").Split(':');
                    s.tagNameToAzm.Add(temp[0], temp[1]);
                }
            }

            // 暂时注释掉ChartSettings相关代码用于最小化验证
            /*
            ChartSettings.set = bool.Parse(Util.GetXmlNodeAttribute(xpathNavigator2, "ChartSetting", "Set"));

            ChartSettings.minDepth = double.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "ChartSetting/minDepth"));
            ChartSettings.ImgColorType = int.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "ChartSetting/ImgColorType"));
            ChartSettings.minGammaImg = double.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "ChartSetting/minGammaImg"));
            ChartSettings.maxGammaImg = double.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "ChartSetting/maxGammaImg"));

            int scnt = int.Parse(Util.GetXmlNodeAttribute(xpathNavigator2, "ChartSetting/Series", "Count"));
            if (scnt > 0)
            {
                int num = 0;
                xpathNodeIterator = (XPathNodeIterator)xpathNavigator2.Evaluate("ChartSetting/Series/Serie");
                while (xpathNodeIterator.MoveNext())
                {
                    XmlElement serie =(XmlElement) xpathNodeIterator.Current.UnderlyingObject;
                    string gr = serie.Attributes[0].Name;
                    string[] range= serie.Attributes[0].Value.Split(':');
                    ChartSettings.serieSettings.Add(new SerieSettings
                    {
                        grName = gr,
                        stroke = Series.DefaultStroke.serieStrokes[num],
                        minVal = double.Parse(range[0]),
                        maxVal = double.Parse(range[1])
                    });

                    num++;
                }
            }
            */

            s.DataTrans = bool.Parse(Util.GetXmlNodeAttribute(xpathNavigator2, "DataTrans", "Value"));
            if (s.DataTrans)
            {
                s.ip = Util.GetFirstXmlNodeValue(xpathNavigator2, "DataTrans/IP");
                s.port = int.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "DataTrans/Port"));
                s.ConnectType = Util.GetFirstXmlNodeValue(xpathNavigator2, "DataTrans/Type") == "TCP" ? ConnectType.TCP : ConnectType.UDP;
                s.DelayLength = double.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "DataTrans/DelayLength"));
                s.StartDepth  = double.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "DataTrans/StartDepth"));
                s.LastSendDepth = double.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "DataTrans/LastSendDepth"));
                s.lastRecSetId = long.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "DataTrans/LastRecSetId"));

                int cnt = int.Parse(Util.GetXmlNodeAttribute(xpathNavigator2, "DataTrans/Wits", "Count"));

                if (cnt > 0)
                {
                    s.WitsCfg.AzmSend = bool.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "DataTrans/Wits/AzmSend"));
                    s.WitsCfg.IncSend = bool.Parse(Util.GetFirstXmlNodeValue(xpathNavigator2, "DataTrans/Wits/IncSend"));

                    xpathNodeIterator = (XPathNodeIterator)xpathNavigator2.Evaluate("DataTrans/Wits/WitsItems/WitsItem");
                    while (xpathNodeIterator.MoveNext())
                    {
                        XmlElement item = (XmlElement)xpathNodeIterator.Current.UnderlyingObject;
                        string gr = item.Attributes[0].Name;
                        string[] temp = item.Attributes[0].Value.Split(':');                        

                        s.WitsCfg.AddWits(new witsItem
                        {
                            GvName = gr,
                            WitsCode = int.Parse(temp[0]),
                            DepthWits = int.Parse(temp[1])
                        });

                    }
                }
            }

            return s;
        }

        private static XmlDocument ToXML() 
        { 
            XmlDocument xml = new XmlDocument();
            XmlElement xmlElement = xml.CreateElement("AppSettingsData");
            xml.AppendChild(xmlElement);
            Util.CreateXmlElement(xmlElement, "AppVersion", Assembly.GetExecutingAssembly().GetName().Version);
            Util.CreateXmlElement(xmlElement, "JobID", Instance.JobID);
            Util.CreateXmlElement(xmlElement, "WellName", Instance.Wellname);
            Util.CreateXmlElement(xmlElement, "Survey", Instance.survey);
            Util.CreateXmlElement(xmlElement, "Interval", Instance.Interval);

            XmlElement xmlElement1 = Util.CreateXmlElement(xmlElement, "GammaCurve", "Count",Instance.GRList.Count.ToString(), null);
            string text = "";
            foreach (string item in Instance.GRList)
            {
                text += item + ",";
            }
            if (Instance.GRList.Count > 0) text = text.Substring(0, text.Length - 1);
            Util.CreateXmlElement(xmlElement1, "GRList", text);
            Util.CreateXmlElement(xmlElement1, "CurveSmooth", Instance.smooth);

            XmlElement xmlElement2 = Util.CreateXmlElement(xmlElement, "GammaImging", "Sector", Instance.sector.ToString(), null);            
            foreach(var xx in Instance.tagNameToAzm)
            {
                Util.CreateXmlElement(xmlElement2, "AzmItem", "Value", xx.Value + ":" + xx.Key, null);
            }

            // 暂时注释掉ChartSettings相关代码用于最小化验证
            /*
            XmlElement xmlElement4 = Util.CreateXmlElement(xmlElement, "ChartSetting", "Set", ChartSettings.set.ToString(), null);
            Util.CreateXmlElement(xmlElement4, "minDepth", ChartSettings.minDepth);
            Util.CreateXmlElement(xmlElement4, "ImgColorType", ChartSettings.ImgColorType);
            Util.CreateXmlElement(xmlElement4, "GammaSetting", ChartSettings.IsGammaSetting);
            Util.CreateXmlElement(xmlElement4, "minGammaImg", ChartSettings.minGammaImg);
            Util.CreateXmlElement(xmlElement4, "maxGammaImg", ChartSettings.maxGammaImg);

            XmlElement xmlElement5 = Util.CreateXmlElement(xmlElement4, "Series", "Count", ChartSettings.serieSettings.Count.ToString(), null);
            foreach (var item in ChartSettings.serieSettings)
            {
                Util.CreateXmlElement(xmlElement5, "Serie", item.grName ,$"{item.minVal:N2}:{item.maxVal:N2}", null);
            }
            */

            XmlElement xmlElement3 = Util.CreateXmlElement(xmlElement, "DataTrans","Value",Instance.DataTrans.ToString(), null);
            Util.CreateXmlElement(xmlElement3, "IP", Instance.ip);
            Util.CreateXmlElement(xmlElement3, "Port", Instance.port);
            Util.CreateXmlElement(xmlElement3, "Type", Instance.ConnectType);
            Util.CreateXmlElement(xmlElement3, "DelayLength", Instance.DelayLength);
            Util.CreateXmlElement(xmlElement3, "StartDepth", Instance.StartDepth.ToString("0.00"));
            Util.CreateXmlElement(xmlElement3, "LastSendDepth", Instance.LastSendDepth.ToString("0.00"));
            Util.CreateXmlElement(xmlElement3, "LastRecSetId", Instance.lastRecSetId);
            
            XmlElement xmlElement6 = Util.CreateXmlElement(xmlElement3, "Wits", "Count", Instance.WitsCfg.Count.ToString(), null);
            Util.CreateXmlElement(xmlElement6, "HasWellName", Instance.WitsCfg.HasWellNm);
            Util.CreateXmlElement(xmlElement6, "HasSurvey", Instance.WitsCfg.HasSurvey);
            Util.CreateXmlElement(xmlElement6, "AzmSend", Instance.WitsCfg.AzmSend);
            Util.CreateXmlElement(xmlElement6, "IncSend", Instance.WitsCfg.IncSend);
            XmlElement xmlElement7 = Util.CreateXmlElement(xmlElement6, "WitsItems");

            var lst = Instance.WitsCfg.GetWitsGVs();
            if (Instance.WitsCfg.HasWellNm)
                lst.Add("WellName");
            foreach(string s in lst)
            {
                var o=Instance.WitsCfg.GetWitsCode(s);
                Util.CreateXmlElement(xmlElement7, "WitsItem", o.GvName , $"{o.WitsCode:0000}:{o.DepthWits:0000}", null);
            }

            return xml;
        }

        public long JobID;
        public string AppVersion;
        public string Wellname;
        public bool survey = false;
        public bool smooth = true;
        public int Interval = 60;
        public List<string> GRList = new List<string>();
        public int CurveCount = 0;
        public int bitRunNo = -1; // 添加bitRunNo属性用于最小化验证

        public bool GammaImging;
        public int sector;
        public Dictionary<string, string> tagNameToAzm = new Dictionary<string, string>();

        public bool DataTrans;
        public string ip;
        public int port;
        public ConnectType ConnectType;
        public WITSEntity WitsCfg = new WITSEntity();

        public double DelayLength;
        public double StartDepth;
        public double LastSendDepth;
        public long lastRecSetId;
        public long lastSvyRecId;

    }
}
