﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.Threading.Tasks;
using System.IO;
using System.Windows;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace TartanLogApp.Models
{
    internal class DataUpload
    {
        private string baseUrl = @"http://47.103.20.175:8200/api";
        private string token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTY5NjkwMzQyMCwiZXhwIjoxNjk3NTA4MjIwfQ.cH3psWP89nJw9XXe4N-eehIM4lSck8ZurbSQErQJzrXMAB0rofUVdSgyQr7WPBsvSWCEUkHjjx8U5aTAtIzIHg";

        public bool Publish(dataType type,object data)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(baseUrl + "/visuallog/data/import");
            request.Method = "POST";
            request.ContentType = "application/json";
            string dataBody = JsonSerializer.Serialize(data);
            bool ret = false;
           
            string postData = "[{\"dataType\":\"" + type.ToString() + "\",\"dataList\":[" + dataBody + "]}]";
            System.Diagnostics.Debug.WriteLine(postData);
            byte[] bytPost = Encoding.UTF8.GetBytes(postData);
            request.ContentLength = bytPost.Length;
            request.Headers.Add("Authorization", "Bearer " + token);

            try
            {
                Stream requestStream = request.GetRequestStream();
                requestStream.Write(bytPost, 0, bytPost.Length);
                requestStream.Close();
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                if (response.StatusCode == HttpStatusCode.OK)
                    ret = true;
                else
                {
                    System.Diagnostics.Debug.WriteLine(response.StatusCode.ToString());
                }
                response.Close();
            }
            catch (WebException e)
            {
                // MessageBox.Show("异常：" + e.Status.ToString());
                System.Diagnostics.Debug.WriteLine("异常：" + e.Status.ToString());
            }

            return ret;
        }

        public string GetJobIDByWell(string wellname)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(baseUrl + $"/job/listByWellNumber?wellNumber={wellname}");
            request.Method = "GET";
            request.Headers.Add("Authorization", "Bearer " + token);

            try
            {
                //Stream requestStream = request.GetRequestStream();               
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                StreamReader sr = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                string str = sr.ReadToEnd();
                sr.Close();
                string ret = "-1";
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    JsonNode json = JsonNode.Parse(str);
                    JsonArray dataArr = json["data"] as JsonArray;
                    
                    foreach (JsonNode node in dataArr)
                    {
                        if ((int)node["jobStatus"] == 0)
                        {
                            ret = node["id"].ToString();
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine(response.StatusCode.ToString());
                }
                response.Close();
                
                return ret;
            }
            catch (WebException e)
            {
                // MessageBox.Show("异常：" + e.Status.ToString());
                System.Diagnostics.Debug.WriteLine("异常：" + e.Status.ToString());
                return "-1";
            }

        }

        public enum dataType
        {
            VL_BIT_RUN_FEATURES,
            VL_BIT_RUNS,
            VL_RESULTS_SET,
            VL_RESULTS_VALUES,
            VL_SURVEY_ANALYSIS,
            VL_TAG_POOL
        }

    }

    public class BitRunsData
    {
        public long bitRunId { get; set; }
        public int run { get; set; }
        public string wellNumber { get; set; }
        public long jobId { get; set; }
    }
    public class BitRunFeaturesData
    {
        public long bitRunFeaturesId { get; set; }
        public long jobId { get; set; }
        public long bitRunId { get; set; }
        public string featureName { get; set; }
        public float bitRunFeatureVal { get; set; }
    }
    public class ResultsSetData
    {
        public long resultsSetId { get; set; }
        public long bitRunId { get; set; }
        public long jobId { get; set; }
        public float bitDepthValue { get; set; }
        public float measDepthValue { get; set; }
        public int offBottom { get; set; }
        public string comment { get; set; }
        public DateTime tDate { get; set; }
    }
    public class ResultValuesData
    {
        public long resultsSetId { get; set; }
        public long resultsRecId { get; set; }
        public long jobId { get; set; }
        public int tagId { get; set; }
        public float valueNum { get; set; }
        public int offBottom { get; set; }
        public DateTime tDate { get; set; }
    }
    public class SurveyAnalysisData
    {
        public long svyRecId { get; set; }
        public long bitRunId { get; set; }
        public long jobId { get; set; }

        public float mDepth { get; set; }
        public float inc { get; set; }
        public float azm { get; set; }
        public float x { get; set; }
        public float y { get; set; }
        public float z { get; set; }
        public float tvd { get; set; }
        public float vs { get; set; }
        public DateTime tDate { get; set; }
    }
}
