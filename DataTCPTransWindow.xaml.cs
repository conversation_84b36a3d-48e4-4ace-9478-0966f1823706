﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Media;
using Avalonia.Threading;
using Avalonia.Interactivity;
using System.Xml;
using TartanLogApp.DataSource;
using TartanLogApp.Models;

namespace TartanLogApp
{
    /// <summary>
    /// DataTCPTransWindow.xaml 的交互逻辑
    /// </summary>
    public partial class DataTCPTransWindow : Window
    {
        //private NetListenerSevice connect;
        private NetConnectSevice connect;

        private DispatcherTimer timer;
        private MeztlDB meztlDB;
        private WITSEntity WITS;
        private bool IsSending;

        private double StartDepth;
        private double LastSentDepth;
        private double DelayLength;
        private long lastRecSetId;
        private long lastSvyRecId;
        private string WellNm;
        private Encoding encoding;
        private string WitsHeader;
        private List<TransItem> lstSendItems;
        private CancellationTokenSource m_cts;
        private bool backgroudreverse = false;

        public DataTCPTransWindow(long jobID, int BitRun, string wellname)
        {
            InitializeComponent();
            string run = BitRun.ToString();
            if (BitRun == -1)
            {
                run = "All";
            }
            Title += " - " + wellname+ " - 趟钻 " + run;
            WellNm = wellname;
            timer = new DispatcherTimer();
            //m_cts = new CancellationTokenSource();
            meztlDB = new MeztlDB(jobID);
            
            meztlDB.CurBitRun = BitRun;
            lstSendItems = new List<TransItem>();            
        }
        
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            IsSending = false;
            WITS = new WITSEntity();
            timer.Interval= TimeSpan.FromSeconds(60);
            timer.Tick += timer_Tick;

            tbDepthDelay.Text = "0";
            tbStartDepth.Text = "0";

            cbGvs.Items.Clear();
            cbGvs.Items.Add("WellName");
            cbGvs.Items.Add("MDpth");

            foreach (string grtagName in meztlDB.GetGRTagNames())
            {
                //if (grtagName.StartsWith("GV") || grtagName.StartsWith("Ga"))
                    cbGvs.Items.Add(grtagName);
            }
            cbGvs.SelectedIndex = 0;

            LoadSettings();
        }

        private void LoadSettings()
        {
            if (UserSettings.Instance.DataTrans)
            {
                tbIP.Text = UserSettings.Instance.ip;
                tbPort.Text = UserSettings.Instance.port.ToString();

                var lst = UserSettings.Instance.WitsCfg.GetWitsGVs();
                if (UserSettings.Instance.WitsCfg.HasWellNm)
                    lst.Add("WellName");
                foreach (string s in lst)
                {
                    if (s != "WellName" && s != "Inc" && s != "TAzm")
                        meztlDB.AddGR(s);
                    var o = UserSettings.Instance.WitsCfg.GetWitsCode(s);
                    //rtbWitsList.AppendText($"{o.GvName}: {o.WitsCode:0000} - {o.DepthWits:0000}\n");
                    listWits.Items.Add($"{o.GvName}: {o.WitsCode:0000} - {o.DepthWits:0000}");
                }
                WITS = UserSettings.Instance.WitsCfg;
                tbDepthDelay.Text = UserSettings.Instance.DelayLength.ToString();
                tbStartDepth.Text = UserSettings.Instance.LastSendDepth.ToString();
            }
            UserSettings.Instance.DataTrans = true;
        }

        private void timer_Tick(object sender, EventArgs e)
        {            
            if (IsSending)
            {
                RefreshData();
            }
        }

        private void RefreshData()
        {
            var x = meztlDB.GetTransData(lastRecSetId, lastSvyRecId, StartDepth, WITS.HasSurvey);
            lastRecSetId = meztlDB.LastResultSetId;
            lastSvyRecId = meztlDB.LastSvyRecID;
            UserSettings.Instance.lastRecSetId = lastRecSetId;
            UserSettings.Instance.lastSvyRecId = lastSvyRecId;

            Dispatcher d = this.Dispatcher;

            if (x.HasSvy)
            {
                double Depth = x.Surveys.Max(o => o.Depth);
                var tranSvy = x.Surveys.Where(u => u.Depth <= Depth - DelayLength).ToList();
                
                List<TransItem> transItems = new List<TransItem>();
                if (WITS.IncSend)
                {
                    transItems.Add(new TransItem { GvName = "Inc" });
                }
                if (WITS.AzmSend)
                {
                    transItems.Add(new TransItem { GvName = "TAzm" });
                }
                //25.7.8 发送井深
                if (WITS.MDpthSend)
                {
                    transItems.Add(new TransItem { GvName = "MDpth" });
                }
                for (int i = 0; i < tranSvy.Count; i++)
                {
                    if (m_cts.Token.IsCancellationRequested) break;
                                        
                    if (WITS.IncSend && WITS.AzmSend)
                    {
                        d.Invoke(new Action(() =>
                        {
                            int m = 0;
                            if (transItems[1].GvName == tranSvy[i].GvName)
                            {
                                m = 1;
                            }

                            transItems[m].Depth = tranSvy[i].Depth;
                            transItems[m].RetVal = tranSvy[i].RetVal;
                            transItems[m].measDepth = tranSvy[i].measDepth;
                            transItems[m].tDate = tranSvy[i].tDate;
                          
                            transItems[1 - m].Depth = tranSvy[i + 1].Depth;
                            transItems[1 - m].RetVal = tranSvy[i + 1].RetVal;
                            transItems[1 - m].measDepth = tranSvy[i + 1].measDepth;
                            transItems[1 - m].tDate = tranSvy[i + 1].tDate;

                            if (WITS.MDpthSend)
                            {
                                transItems[2].Depth = tranSvy[i].Depth;
                                transItems[2].RetVal = tranSvy[i].measDepth;
                                transItems[2].measDepth = tranSvy[i].measDepth;
                                transItems[2].tDate = tranSvy[i].tDate;
                            }
                            Thread.Sleep(50);
                            connect.Send(WitsHeader, transItems, WITS, encoding);
                        }), DispatcherPriority.Background);

                        i++;
                    }
                    else
                    {
                        if (transItems[0].GvName == tranSvy[i].GvName)
                        {
                            transItems[0].Depth = tranSvy[i].Depth;
                            transItems[0].RetVal = tranSvy[i].RetVal;
                            transItems[0].measDepth = tranSvy[i].measDepth;
                            transItems[0].tDate = tranSvy[i].tDate;
                            if (WITS.MDpthSend)
                            {
                                transItems[1].Depth = tranSvy[i].Depth;
                                transItems[1].RetVal = tranSvy[i].measDepth;
                                transItems[1].measDepth = tranSvy[i].measDepth;
                                transItems[1].tDate = tranSvy[i].tDate;
                            }
                            d.Invoke(new Action(() =>
                                {
                                    Thread.Sleep(50);
                                connect.Send(WitsHeader, transItems, WITS, encoding);
                            }), DispatcherPriority.Background);
                        }
                    }

                }
                
            }

            if (x.HasGv)
            {
                double maxDepth = x.GVDatas.Max(o => o.measDepth);
                var trans = x.GVDatas.Where(u => u.measDepth <= maxDepth - DelayLength).OrderBy(o => o.measDepth).ToList();
                int k = meztlDB.SectorSize;

                List<TransItem> transItems = new List<TransItem>();
                foreach (string item in meztlDB.GetGvList())
                {
                    transItems.Add(new TransItem { GvName = item, Depth = -999 });
                }

                foreach (var item in trans)
                {
                    if (m_cts.Token.IsCancellationRequested) break;

                    d.Invoke(new Action(() =>
                    {
                        for (int i = 0; i < transItems.Count; i++)
                        {
                            if (transItems[i].GvName == item.GvName)
                            {
                                //
                                transItems[i].Depth = item.Depth;
                                transItems[i].RetVal = item.RetVal;
                                transItems[i].measDepth = item.measDepth;
                                transItems[i].tDate = item.tDate;
                            }

                        }
                        Thread.Sleep(50);
                        connect.Send(WitsHeader, transItems, WITS, encoding);
                    }), DispatcherPriority.Background);

                }

            }

        }

        private void btnConnect_Click(object sender, RoutedEventArgs e)
        {
            string host = tbIP.Text.Trim();
            string ports = tbPort.Text.Trim();

            if (!string.IsNullOrEmpty(host) && !string.IsNullOrEmpty(ports))
            {
                try
                {
                    int port = Convert.ToInt32(ports);
                    if (rbTCP.IsChecked == true)
                    {
                        //connect=new NetListenerSevice(ConnectType.TCP, port);
                        connect = new NetConnectSevice(host, port, ConnectType.TCP);
                    }
                    else
                    {
                        //connect = new NetListenerSevice(ConnectType.UDP, port);
                        connect = new NetConnectSevice(host, port, ConnectType.UDP);
                    }
                    connect.OnSendEvent += Sent;
                    //connect.StartListen();
                    connect.Connect();
                    Thread.Sleep(200);

                    //if (connect.IsStartListening)
                    if (connect.IsConnected)
                    {
                        lblStatus.Fill = Brushes.Lime;
                        btnConnect.IsEnabled = false;

                        UserSettings.Instance.ConnectType = connect.ConnectType;
                        UserSettings.Instance.ip = host;
                        UserSettings.Instance.port = port;                        
                    }
                    else
                    {
                        MessageBox.Show("连接失败！");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                    return ;
                }

            }

            
        }

        private void Sent(List<TransItem> transItems)
        {
            backgroudreverse = !backgroudreverse;
            Dispatcher dp = this.Dispatcher;
            dp.Invoke(new Action(() =>
            {
                string str = WitsHeader;
                foreach (TransItem transItem in transItems)
                {
                    LastSentDepth = transItem.measDepth;
                    UserSettings.Instance.LastSendDepth = LastSentDepth;

                    lstSendItems.Add(new TransItem 
                    {                     
                        GvName = transItem.GvName,
                        measDepth = transItem.measDepth,
                        Depth = transItem.Depth,
                        tDate = transItem.tDate,
                        RetVal = transItem.RetVal
                    });
                    witsItem wits = WITS.GetWitsCode(transItem.GvName);
                    str += wits.WitsCode.ToString("0000") + transItem.RetVal.ToString("0.00") + "\r\n";
                    str += wits.DepthWits.ToString("0000") + transItem.Depth.ToString("0.00") + "\r\n";

                    ListViewItem viewItem = new ListViewItem();
                    viewItem.Background = backgroudreverse ? Brushes.AliceBlue : Brushes.Beige;
                    viewItem.Content = new
                    {
                        SendTime = DateTime.Now.ToString("G"),
                        MeasureDepth = transItem.measDepth.ToString("0.00"),
                        TagName = transItem.GvName,
                        OrignalVal = transItem.RetVal.ToString("0.00"),
                        Timestamp = transItem.tDate,
                        SendString = $"depth:{transItem.Depth:0.00} value:{transItem.RetVal:0.00}"
                    };
                    viewItem.HorizontalContentAlignment = HorizontalAlignment.Center;
                    lstSentData.Items.Add(viewItem);
                }
                str += "!!\r\n";
                tbLastSentLabel.Text = str;
                lstSentData.SelectedIndex = lstSentData.Items.Count - 1;
                lstSentData.ScrollIntoView(lstSentData.SelectedItem);
            }));
           
        }
       
        private void btnDisConnect_Click(object sender, RoutedEventArgs e)
        {
            //connect?.Stop();
            m_cts?.Cancel();
            connect?.Close();
            
            lblStatus.Fill = Brushes.Red;
            btnConnect.IsEnabled = true;
            tbLastSentLabel.Text = "";

            IsSending = false;
            timer.Stop();
            btnSettingSend.IsEnabled = true;
            btnAddWits.IsEnabled = true;
            btnWitsClear.IsEnabled = true;
            tbLastSentLabel.Text = "";
        }

        private void btnSettingSend_Click(object sender, RoutedEventArgs e)
        {
            if (connect == null || !connect.IsConnected)
            {
                lblStatus.Fill = Brushes.Red;
                btnConnect.IsEnabled = true;
                MessageBox.Show("未连接或连接已断开！");
                return;
            }

            if (WITS.Count <1)
            {
                MessageBox.Show("请添加项目！");
                return;
            }

            try
            {
                StartDepth = double.Parse(tbStartDepth.Text.Trim());
                DelayLength = double.Parse(tbDepthDelay.Text.Trim());
               
                switch (cbCharset.SelectedIndex)
                {
                    case 0:
                        encoding = Encoding.UTF8;
                        break;
                    case 1:
                        encoding = Encoding.Unicode;
                        break;
                    case 2:
                        encoding = Encoding.ASCII;
                        break;
                    case 3:
                        encoding = Encoding.GetEncoding("GB2312");
                        break;
                    default:
                        encoding = Encoding.UTF8;
                        break;
                }

            }
            catch (Exception)
            {
                MessageBox.Show("输入数值不正确！");
                return;
            }

            UserSettings.Instance.WitsCfg = WITS;
            UserSettings.Instance.DelayLength = DelayLength;
            UserSettings.Instance.StartDepth = StartDepth;

            //25.7.23  先保存配置
            UserSettings.SaveXML("config.xml");
            WitsHeader = "&&\r\n";
            if (WITS.HasWellNm)
            {
                WitsHeader += WITS.GetWitsCode("WellName").WitsCode.ToString("0000") + WellNm + "\r\n";
            }

            btnSettingSend.IsEnabled = false;
            IsSending = true;
            lastRecSetId = 0;

            m_cts= new CancellationTokenSource();

            timer.Start();
            RefreshData();
            
            btnAddWits.IsEnabled = false;
            btnWitsClear.IsEnabled = false;
        }

        private void btnSendStop_Click(object sender, RoutedEventArgs e)
        {
            m_cts?.Cancel();
            IsSending = false;
            timer.Stop();
            
            btnSettingSend.IsEnabled = true;
            btnAddWits.IsEnabled = true;
            btnWitsClear.IsEnabled = true;
            tbLastSentLabel.Text = "";
        }

        private void btnWitsClear_Click(object sender, RoutedEventArgs e)
        {
            WITS.WitsClear();
            meztlDB.ClearGR();
            ////rtbWitsList.Document = new FlowDocument();
            //rtbWitsList.Document.Blocks.Clear();
            //rtbWitsList.Document.Blocks.Add(new Paragraph());
            listWits.Items.Clear();
            
        }

        private void btnAddWits_Click(object sender, RoutedEventArgs e)
        {
            string gr = cbGvs.Text;
            int witscode, witsdepth;
            if (gr == "WellName")
            {

            }
            else if (gr == "Inc" || gr == "TAzm" || gr == "MDpth")
            {
                
            }
            else
            {
                meztlDB.AddGR(gr);
            }

            witscode = int.Parse(tbWits.Text.Trim());
            witsdepth = int.Parse(tbWitsDepth.Text.Trim());

            WITS.AddWits(new witsItem
            {
                GvName = gr,
                WitsCode = witscode,
                DepthWits = witsdepth
            });

            //rtbWitsList.AppendText($"{gr}: {witscode:0000} - {witsdepth:0000}\n");
            listWits.Items.Add($"{gr}: {witscode:0000} - {witsdepth:0000}");
        }

        private void btnlstClear_Click(object sender, RoutedEventArgs e)
        {
            lstSendItems.Clear();
            lstSentData.Items.Clear();
        }

        private void btnlstSave_Click(object sender, RoutedEventArgs e)
        {
            if (lstSendItems.Count > 0)
            {
                string filepath = "";
                SaveFileDialog openFileDialog = new SaveFileDialog
                {
                    Title = "保存数据",
                    AddExtension = true,
                    Filter = "csv文件(*.csv)|*.csv"
                };
                if (openFileDialog.ShowDialog() == true)
                {
                    filepath = openFileDialog.FileName;
                }
                else
                {
                    return;
                }

                List<CSVRow> rows = new List<CSVRow>();
                CSVRow r = new CSVRow();
                r.Add("序号");
                r.Add("测深");
                r.Add("标签");
                r.Add("数值");
                r.Add("数据时间");
                r.Add("传输数据");
                rows.Add(r);
                int i = 0;
                foreach (var item in lstSendItems)
                {
                    i++;
                    CSVRow rr = new CSVRow();
                    rr.Add(i);
                    rr.Add(item.measDepth.ToString("0.00"));
                    rr.Add(item.GvName);
                    rr.Add(item.RetVal.ToString("0.00"));
                    rr.Add(item.tDate);
                    rr.Add($"depth:{item.Depth:0.00} value:{item.RetVal:0.00}");

                    rows.Add(rr);
                }

                Util.SaveCSV(filepath, rows);

                MessageBox.Show("保存完成！");
            }
        }

        private void Window_Closed(object sender, EventArgs e)
        {
            UserSettings.SaveXML("config.xml");
            this.Tag = "close";
        }

        private void btnWitDelete_Click(object sender, RoutedEventArgs e)
        {
            var item = listWits.SelectedItem;
            //int c = listWits.SelectedIndex;
            if (item == null)
            {
                MessageBox.Show("先选择要删除的项目！");
                return;
            }

            string gr = item.ToString().Split(':')[0];
            
            WITS.WitsDel(gr);
          
            if (gr == "WellName" || gr == "Inc" || gr == "TAzm" || gr == "MDpth")
            {

            }
            else
            {
                meztlDB.DelGR(gr);
            }
            listWits.Items.Remove(item);
            //MessageBox.Show(wname);
        }
    }
}
