using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using TartanLogApp.Models;
using System.Data.OleDb;
using System.Data.Common;

namespace TartanLogApp.DataSource
{
    class MeztlDB
    {
        private string sqlserverConnStr = "Data Source=.\\SQLEXPRESS;Initial Catalog =MEZINTELMWD;Integrated Security=true;";
        //private string sqlserverConnStr = "Data Source=.;Initial Catalog =MEZINTELMWD;Integrated Security=true;";

        public long jobID { get; set; }
        public int CurBitRun { get; set; }
        public string WellName { get; private set; }

        public bool Survey;
        public bool MWD;
        

        private List<string> tagNameList = new List<string>();
        public int SectorSize => tagNameList.Count;
        private Dictionary<string, int> tagNameToID = new Dictionary<string, int>();

        public List<BitRun> BitRuns = new List<BitRun>();
        public int TotalRuns => BitRuns.Count;
        public DataTable dtResults;
        public string strBitRuns { get; private set; }

        public MeztlDB(long jobid, bool survey = true, bool mWD = false)
        {
            jobID = jobid;
            WellName = GetWellName();
            GetBitRuns();
            Survey = survey;
            MWD = mWD;
            CurBitRun = -1;
        }

        public int AddGR(string name)
        {
            if (string.IsNullOrEmpty(name))
                return -1;
            if(tagNameList.Contains(name))
                return -1;
            
            //var tb = FromDB($"SELECT * FROM DOM_BitRunFeatures WHERE FeatureName in ('{name}Scale','{name}Offset')");
            //if (tb.Rows.Count < 2)
            //    return -1;

            tagNameList.Add(name);

            return tagNameList.Count;
        }
        
        public bool DelGR(string name)
        {
            if (string.IsNullOrEmpty(name))
                return false;
            if (!tagNameList.Contains(name))
                return false;

            return tagNameList.Remove(name);
        }

        public void ClearGR()
        {
            //BitRuns.Clear();
            tagNameList.Clear();
        }

        public void ReadFeaturesVal()
        {
            if (SectorSize < 1) return;
            int k = 0;
            tagNameToID.Clear();
            foreach (string s in tagNameList)
            {
                tagNameToID.Add(s, k++);
            }

            foreach (BitRun run in BitRuns)
            {
                run.features = new List<GR_feature>();
                run.SurveyToBit = GetFeatureVal("Survey-to-Bit (PTB)", run.Id);
                run.GammaToBit = GetFeatureVal("Gamma-to-Bit (GTB)", run.Id);
                foreach (string tag in tagNameList)
                {
                    GR_feature f = new GR_feature();
                    f.FeatureName = tag;
                    if (tag == "Gama")
                    {
                        f.GROffset = 0;
                        f.GRScale = GetFeatureVal("Gamma Scale Factor", run.Id);

                    }
                    else
                    {
                        f.GROffset = GetFeatureVal(tag + "Offset", run.Id);
                        f.GRScale = GetFeatureVal(tag + "Scale", run.Id);
                    }

                    run.features.Add(f);
                }
            }
        }

        private DataTable GetData(long lastResultsetid = 0)
        {
            DataTable dt;
            string tags = "";
            /*
            if (Survey)
            {
                tags = "'TAzm','Inc',";
            }
            else
            {
                tags = "";
            }
            */
            foreach (string tag in tagNameList)
            {
                tags += "'" + tag + "',";
            }

            string sql;
            if (CurBitRun == -1)            
                sql = $"select BitRunID,MeasDepthValue,TagName,ValueNum,TDate,ResultRecID,ResultsSetID from vw_Results_Indexed where OffBottom=0 and MeasDepthValue>0 " +
                    $"and JobID={jobID} and TagName in ({tags.Substring(0, tags.Length - 1)}) and ResultsSetID>{lastResultsetid} order by ResultRecID desc";                    
            else
            {
                var bitrun = BitRuns.FirstOrDefault(o => o.No == CurBitRun);
                sql = $"select BitRunID,MeasDepthValue,TagName,ValueNum,TDate,ResultRecID,ResultsSetID from vw_Results_Indexed where OffBottom=0 and MeasDepthValue>0 " +
                $"and BitRunID={bitrun.Id} and TagName in ({tags.Substring(0, tags.Length - 1)}) and ResultsSetID>{lastResultsetid} order by ResultRecID desc";
            }

            dt = FromDB(sql);
            if (dt.Rows.Count > 0)
                LastResultSetId = (long)dt.Rows[0]["ResultsSetID"];

            return dt;
        }

        public MezDataSet GetDataSet(long lastResultsetid = 0)
        {
            DataTable dt = GetData(lastResultsetid);
           
            if (dt.Rows.Count < SectorSize)
            {
                return null;
            }
            dt.Columns.Add(new DataColumn("ResultValue", typeof(float)));

            MezDataSet mezData = new MezDataSet();
            mezData.GrList = new GrData[SectorSize];

            foreach (string s in tagNameList)
            {
                tagNameToID.TryGetValue(s, out int k);
                mezData.GrList[k] = new GrData();
                mezData.GrList[k].GrName = s;
                mezData.GrList[k].grList = new List<tagDataPoint>();
            }

            if (Survey)
            {                
                string sql = $"select * from LOG_SurveyAnalysis where BitRunID in ({strBitRuns}) and SvyBad=1 and ResultsSetID>{LastResultSetId} order by SvyRecID";
                var dt2 = FromDB(sql);
                mezData.SvyList = new List<surveyPoint>();
                for (int i = 0; i < dt2.Rows.Count; i++)
                {
                    long bitID = (long)dt2.Rows[i]["BitRunID"];
                    var bitrun = BitRuns.FirstOrDefault(o => o.Id == bitID);
                    mezData.SvyList.Add(new surveyPoint
                    {
                        MDepth = (float)(dt2.Rows[i]["MDepth"]),
                        Inc = (float)(dt2.Rows[i]["Inc"]),
                        Azm = (float)(dt2.Rows[i]["Azm"]),
                        TVD = (float)(dt2.Rows[i]["Z"])
                    });
                }

            }

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                long bitID = (long)dt.Rows[i]["BitRunID"];
                var bitrun = BitRuns.FirstOrDefault(o => o.Id == bitID);
                string tag = dt.Rows[i]["TagName"].ToString();

                double dp = (float)(dt.Rows[i]["MeasDepthValue"]);
                double v = float.Parse(dt.Rows[i]["ValueNum"].ToString());
                double ret = v;

                int tagID;
                tagNameToID.TryGetValue(tag, out tagID);

                if (MWD || tag == "Gama")
                {
                    ret = v * bitrun.features.FirstOrDefault(o => o.FeatureName == tag).GRScale;
                    mezData.GrList[tagID].grList.Add(new tagDataPoint
                    {
                        depth = dp - bitrun.GammaToBit,
                        val = ret
                    });
                }
                else
                {
                    ret = v * bitrun.features.FirstOrDefault(o => o.FeatureName == tag).GRScale;
                    mezData.GrList[tagID].grList.Add(new tagDataPoint
                    {
                        depth = dp - bitrun.features.FirstOrDefault(o => o.FeatureName == tag).GROffset,
                        val = ret
                    });
                }
                
                dt.Rows[i]["ResultValue"] = (float)Math.Round(ret,2);
            }
            dtResults = dt;
            return mezData;

        }

        public long LastResultSetId = 0;
        public long LastSvyRecID = 0;
        public TransData GetTransData(long lastResultsetid, long lastSvyRecID, double curDepth, bool survey)
        {
            DataTable dt,dt1;
            string tags = "", sql;
            TransData transData = new TransData();
            transData.HasSvy = false;
            transData.HasGv = false;
           
            if (SectorSize > 0)
            {
                foreach (string tag in tagNameList)
                {
                    tags += "'" + tag + "',";
                }
                if (CurBitRun == -1)
                    sql = $"select BitRunID,MeasDepthValue,TagName,ValueNum,TDate,TVD,ResultRecID,ResultsSetID from vw_Results_Indexed where OffBottom=0 and MeasDepthValue>{curDepth} " +
                        $"and JobID={jobID} and TagName in ({tags.Substring(0, tags.Length - 1)}) and ResultsSetID>{lastResultsetid} order by ResultRecID desc";
                else
                {
                    var bitrun = BitRuns.FirstOrDefault(o => o.No == CurBitRun);
                    sql = $"select BitRunID,MeasDepthValue,TagName,ValueNum,TDate,ResultRecID,ResultsSetID from vw_Results_Indexed where OffBottom=0 and MeasDepthValue>{curDepth} " +
                    $"and BitRunID={bitrun.Id} and TagName in ({tags.Substring(0, tags.Length - 1)}) and ResultsSetID>{lastResultsetid} order by ResultRecID desc";
                }
                dt = FromDB(sql);
                if (dt.Rows.Count > 0)
                {
                    LastResultSetId = (long)dt.Rows[0]["ResultsSetID"];

                    ReadFeaturesVal();
                    transData.HasGv = true;
                    List<TransItem> transItems = new List<TransItem>();
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        long bitID = (long)dt.Rows[i]["BitRunID"];
                        var bitrun = BitRuns.FirstOrDefault(o => o.Id == bitID);
                        string tag = dt.Rows[i]["TagName"].ToString();

                        double dp = (float)(dt.Rows[i]["MeasDepthValue"]);
                        double v = float.Parse(dt.Rows[i]["ValueNum"].ToString());
                        double ret = v;

                        TransItem item = new TransItem();
                        item.GvName = tag;
                        item.tDate = dt.Rows[i]["TDate"].ToString();
                        item.measDepth = dp;
                        if (tag == "TAzm" || tag == "Inc")
                        {
                            item.Depth = dp - bitrun.SurveyToBit;
                            item.RetVal = v;
                        }
                        else //if (tag.StartsWith("GV") || tag.StartsWith("Ga"))
                        {
                            int tagID;
                            tagNameToID.TryGetValue(tag, out tagID);

                            if (tag == "Gama")
                            {
                                ret = v * bitrun.features.FirstOrDefault(o => o.FeatureName == tag).GRScale;
                                //测深
                                item.Depth = dp - bitrun.GammaToBit;
                                //25.7.8 井深
                                item.measDepth = dp;
                                item.RetVal = ret;

                            }
                            else
                            {
                                ret = v * bitrun.features.FirstOrDefault(o => o.FeatureName == tag).GRScale;
                                item.Depth = dp - bitrun.features.FirstOrDefault(o => o.FeatureName == tag).GROffset;
                                //25.7.8 增加测深
                                item.measDepth = dp;
                                item.RetVal = ret;
                            }
                        }
                        //else 
                        //{
                        //    item.measDepth = dp;
                        //    item.RetVal = ret; 
                        //}

                        transItems.Add(item);
                    }

                    transData.GVDatas = transItems;
                }

            }

            if (survey)
            {
                if (CurBitRun == -1)
                    sql = $"select * from LOG_SurveyAnalysis where BitRunID in ({strBitRuns}) and SvyBad=1 and SvyRecID>{lastSvyRecID} and MDepth>{curDepth} order by SvyRecID";
                else
                {
                    var bitrun = BitRuns.FirstOrDefault(o => o.No == CurBitRun);
                    sql = $"select * from LOG_SurveyAnalysis where BitRunID={bitrun.Id} and SvyBad=1 and SvyRecID>{lastSvyRecID} and MDepth>{curDepth} order by SvyRecID";
                }
                dt1 = FromDB(sql);
                if (dt1.Rows.Count > 0)
                {
                    LastSvyRecID = (long)dt1.Rows[0]["SvyRecID"];
                    transData.HasSvy = true;

                    List<TransItem> transItems = new List<TransItem>();

                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        double dp = (float)dt1.Rows[i]["MDepth"];
                        double inc = (float)dt1.Rows[i]["Inc"];
                        double azm = (float)dt1.Rows[i]["Azm"];
                        
                        long bitID = (long)dt1.Rows[i]["BitRunID"];
                        var bitrun = BitRuns.FirstOrDefault(o => o.Id == bitID);

                        TransItem item = new TransItem
                        {
                            GvName = "Inc",
                            tDate = dt1.Rows[i]["TDate"].ToString(),
                            RetVal = inc,
                            //25.7.8 Survey表中的测深 + 零长 返回井深
                            measDepth = dp,
                            Depth = dp + bitrun.SurveyToBit
                        };
                        TransItem item1 = new TransItem
                        {
                            GvName = "TAzm",
                            tDate = dt1.Rows[i]["TDate"].ToString(),
                            RetVal = azm,
                            //25.7.8 Survey表中的测深 + 零长 返回井深
                            measDepth = dp,
                            Depth = dp + bitrun.SurveyToBit
                        };

                        transItems.Add(item);
                        transItems.Add(item1);
                    }
                    
                    transData.Surveys = transItems;
                }

            }

            return transData;
        }

        public int UpdateMezDepth(LoggingData logging, out string ret)
        {
            DateTime BaseTime = DateTime.Parse("1904-1-1 8:0:0");
      
            // Insert ResultSets           
            var dt = FromDB("select top 1 [BitRunID],[BitDepthValue],[MeasDepthValue],[TVD],[OffBottom],[Annotation],[Comment],[TDate]," +
                       "[TimeSec],[GranularityFlag],[ManuallyAdded],[TVDDone],[AvCF],[AvQF],[PDcd],[WdCF],[WdQF],[WdPA] from [LOG_ResultsSet] order by ResultsSetID desc");
            if (dt.Rows.Count>0)
            {
                long BitRunId = Convert.ToInt64(dt.Rows[0]["BitRunID"].ToString());

                double bitdepth = logging.BitDepth;
                double measdepth = logging.BitDepth;

                float tvd = Convert.ToSingle(dt.Rows[0]["TVD"].ToString());
                int offbotm = Convert.ToInt32(dt.Rows[0]["OffBottom"].ToString());
                int annot = Convert.ToInt32(dt.Rows[0]["Annotation"].ToString());

                DateTime dateTime = DateTime.Now;
                long timesec = (long)(dateTime - BaseTime).TotalSeconds;
                
                int granu = Convert.ToInt32(dt.Rows[0]["GranularityFlag"].ToString());
                int mannually = Convert.ToInt32(dt.Rows[0]["ManuallyAdded"].ToString());
                int tvddone = Convert.ToInt32(dt.Rows[0]["TVDDone"].ToString());

                float avcf = NullConvert(dt.Rows[0]["AvCF"].ToString());
                float avqf = NullConvert(dt.Rows[0]["AvQF"].ToString());
                float pdcd = NullConvert(dt.Rows[0]["PDcd"].ToString());
                float wdcf = NullConvert(dt.Rows[0]["WdCF"].ToString());
                float wdqf = NullConvert(dt.Rows[0]["WdQF"].ToString());
                float wdpa = NullConvert(dt.Rows[0]["WdPA"].ToString());
                // Insert ResultValues
                string vals = $"{BitRunId},{bitdepth},{measdepth},{tvd},{offbotm},{annot},'TartanLogApp','{dateTime:G}',{timesec}," +
       $"{granu},{mannually},{tvddone},{avcf},{avqf},{pdcd},{wdcf},{wdqf},{wdpa}";
                
                Connection = new SqlConnection(sqlserverConnStr);
                Connection.Open();

                int k = InsertData($"insert into [LOG_ResultsSet]([BitRunID],[BitDepthValue],[MeasDepthValue],[TVD],[OffBottom],[Annotation],[Comment]," +
                    $"[TDate],[TimeSec],[GranularityFlag],[ManuallyAdded],[TVDDone],[AvCF],[AvQF],[PDcd],[WdCF],[WdQF],[WdPA]) values ({vals})");
               
                Connection.Close();

                ret = $"Insert TDate:{dateTime:G} Depth:{logging.BitDepth}";
                return k;
            }

            ret = "";
            return 0;

        }


        private string GetWellName()
        {
            string sql = $"SELECT JobID,ParamValue as wellNm FROM LOG_JobHdrParams where HdrParamID=19 and JobID = {jobID}";
            var dt = FromDB(sql);
            if (dt.Rows.Count > 0)
            {
                return dt.Rows[0]["wellNm"].ToString();
            }
            return null;
        }

        private void GetBitRuns()
        {
            string sql = $"select run.BitRunID,run.BitRunNo,leg.LegName,leg.LegID from LOG_JobLegBitRuns run inner join LOG_JobLegs leg on run.LegID = leg.LegID " +
                $"inner join LOG_Job on LOG_Job.JobID= leg.JobID and LOG_Job.JobID = {jobID} ORDER BY run.BitRunID";

            var dt = FromDB(sql);
            if (dt.Rows.Count > 0)
            {
                strBitRuns = "";
                BitRuns.Clear();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    BitRuns.Add(new BitRun
                    {
                        Id = (long)dt.Rows[i]["BitRunID"],
                        No = (int)dt.Rows[i]["BitRunNo"],
                        LegId = (long)dt.Rows[i]["LegID"],
                        LegName = dt.Rows[i]["LegName"].ToString(),
                    });
                    strBitRuns += dt.Rows[i]["BitRunID"].ToString() + ",";
                }
                strBitRuns = strBitRuns.Substring(0, strBitRuns.Length - 1);
            }
        }

        private double GetFeatureVal(string featureName, long bitRunId)
        {
            string sql = $"select BitRunFeatureVal from vw_BitRunFeatures where BitRunID={bitRunId} and FeatureName='{featureName}'";
            var dt = FromDB(sql);
            if (dt.Rows.Count > 0)
            {
                return double.Parse(dt.Rows[0]["BitRunFeatureVal"].ToString());
            }
            else
            {
                if (featureName.Contains("Scale"))
                {
                    return 1;
                }
                if (featureName.EndsWith("Offset"))
                {
                    return 0;
                }

                return -1;
            }            
        }

        public List<string> GetGRTagNames()
        {
            List<string> ret= new List<string>();
            string sql;
            if (CurBitRun == -1)
            {
                sql = $"select distinct(TagName) from [vw_Results] where [JobID]={jobID}";
            }
            else
            {
                var bitrun = BitRuns.FirstOrDefault(o => o.No == CurBitRun);
                sql = $"select distinct(TagName) from [vw_Results] where BitRunID={bitrun.Id}";
            }

            var dt = FromDB(sql);
            if (dt.Rows.Count > 0)
            {
                foreach (DataRow row in dt.Rows)
                {
                    ret.Add(row["TagName"].ToString());
                }
            }
            return ret;
        }

        public List<string> GetGvList() 
        {
            return tagNameList;
        }

        private DataTable GetTagsPool()
        {
            var str = GetGRTagNames();
            string ss = "";
            foreach (var tag in str)
            {
                ss += $"'{tag}',";
            }
            string sql = $"select * from DOM_TagPool where TagName in ({ss.Substring(0, ss.Length - 1)})";
            return FromDB(sql);
        }
        private DataTable GetBitRunFeatures()
        {
            string sql = $"select BitRunID, FeatureName, BitRunFeatureVal from vw_BitRunFeatures where BitRunID in ({strBitRuns})";
            return FromDB(sql);
        }
        private DataTable GetResultSet()
        {
            string sql = $"select * from LOG_ResultsSet where ResultsSetID in (select distinct(ResultSetID) from LOG_ResultsValues val join LOG_ResultsSet rr on val.ResultSetID=rr.ResultsSetID and rr.[BitRunID] in ({strBitRuns})) order by ResultsSetID";
            return FromDB(sql);
        }
        private DataTable GetResultValues()
        {
            string sql = $"select LOG_ResultsValues.* from LOG_ResultsValues inner join LOG_ResultsSet on LOG_ResultsValues.ResultSetID = LOG_ResultsSet.ResultsSetID and LOG_ResultsSet.BitRunID in ({strBitRuns})  order by ResultRecID";
            return FromDB(sql);
        }
        private DataTable ReadSurvey()
        {
            string sql = $"select * from LOG_SurveyAnalysis where [BitRunID] in ({strBitRuns}) order by [SvyRecID]";
            return FromDB(sql);
        }
        private DataTable ReadSurveyParking()
        {
            string sql = $"select * from LOG_SurveyParking where [JobID]={jobID} order by [SvyParkingID]";
            return FromDB(sql);
        }

        public void SaveJobData(string filename)
        {
            if (TotalRuns > 0)
            {
                ExportToExcel toExcel = new ExportToExcel();
                if (!toExcel.CheckExcel(filename))
                {
                    return;
                }

                DataTable dt = new DataTable();
                dt.Columns.Add("WellName", typeof(string));
                dt.Columns.Add("JobID", typeof(long));
                dt.Columns.Add("LegName", typeof(string));
                dt.Columns.Add("LegID", typeof(long));
                dt.Columns.Add("BitRunID", typeof(long));
                dt.Columns.Add("BitRunNo", typeof(int));
                foreach (var item in BitRuns)
                {
                    DataRow row = dt.NewRow();
                    row["WellName"] = WellName;
                    row["LegName"] = item.LegName;
                    row["JobID"] = jobID;
                    row["LegID"] = item.LegId;
                    row["BitRunID"] = item.Id;
                    row["BitRunNo"] = item.No;

                    dt.Rows.Add(row);
                }

                toExcel.SaveExcel(dt, "", filename, "LegAndBitRuns");

                var feature = GetBitRunFeatures();
                toExcel.SaveExcel(feature, "", filename, "BitRunFeatures");

                var tagpool = GetTagsPool();
                toExcel.SaveExcel(tagpool, "", filename, "TagPool");

                var resultSet = GetResultSet();
                var resultValues = GetResultValues();
                toExcel.SaveExcel(resultSet, "", filename, "ResultsSet");
                toExcel.SaveExcel(resultValues, "", filename, "ResultsValues");
                var svy = ReadSurvey();
                var svyParking = ReadSurveyParking();
                toExcel.SaveExcel(svy, "", filename, "SurveyAnalysis");
                toExcel.SaveExcel(svyParking, "", filename, "SurveyParking");

                // MessageBox.Show("保存完成！"); // 暂时注释掉用于最小化验证
                System.Diagnostics.Debug.WriteLine("保存完成！");
            }
        }

        public bool JobReStore(string excelfile)
        {
            var bits = ExToTab("LegAndBitRuns$", excelfile);
            long jobid = Convert.ToInt64(bits.Rows[0]["JobID"].ToString());
            string well = bits.Rows[0]["WellName"].ToString();

            Connection = new SqlConnection(sqlserverConnStr);
            Connection.Open();

            System.Diagnostics.Debug.WriteLine("Read BitRun - Connection Open...");
            int ret;
            //新建jobID
            string sql = $"insert into LOG_Job(HostComputer) values('Insert{jobid}')";

            ret = InsertData(sql);
            if (ret != 1) return false;
            System.Diagnostics.Debug.WriteLine("Insert Job");

            long newId;
            var dt = FromDB("select top 1 JobID from LOG_Job order by JobID desc");
            newId = (long)dt.Rows[0][0];

            //插入井号
            sql = $"insert into LOG_JobHdrParams(JobID,HdrParamID,ParamValue) values({newId},19,'{well}')";
            ret = InsertData(sql);
            if (ret != 1) return false;
            System.Diagnostics.Debug.WriteLine("Insert WellNm");

            //插入Leg 与 BitRun
            long lastlegId = 0;
            long newLegId = 0;

            Dictionary<long, long> dictBitRunID = new Dictionary<long, long>();

            foreach (DataRow item in bits.Rows)
            {

                long tempId = Convert.ToInt64(item["LegID"].ToString());

                if (tempId != lastlegId)
                {
                    sql = $"insert into LOG_JobLegs(JobID,LegName) values({newId},'{item["LegName"]}')";
                    ret = InsertData(sql);
                    if (ret != 1) return false;
                    lastlegId = tempId;
                    var tmp = FromDB($"select LegID from LOG_JobLegs where JobID={newId} order by LegID desc");
                    if (tmp.Rows.Count < 1)
                    {
                        return false;
                    }
                    newLegId = (long)tmp.Rows[0][0];
                }

                int no = Convert.ToInt32(item["BitRunNo"].ToString());

                sql = $"insert into LOG_JobLegBitRuns(LegID,BitRunNo) values({newLegId},{no})";
                ret = InsertData(sql);
                if (ret != 1) return false;
                var tmpDB = FromDB($"select BitRunID from LOG_JobLegBitRuns where LegID={newLegId} and BitRunNo={no}");
                if (tmpDB.Rows.Count < 1)
                {
                    return false;
                }

                dictBitRunID.Add(Convert.ToInt64(item["BitRunID"].ToString()), (long)tmpDB.Rows[0]["BitRunID"]);

            }

            System.Diagnostics.Debug.WriteLine("Insert Legs & BitRuns");

            //插入BitRunFeatures
            var dt1 = ExToTab("BitRunFeatures$", excelfile);
            for (int i = 0; i < dt1.Rows.Count; i++)
            {
                string featureTag = dt1.Rows[i]["FeatureName"].ToString();
                sql = $"select [BitRunFeatureID] from [DOM_BitRunFeatures] where [FeatureName]='{featureTag}'";
                var tmp = FromDB(sql);
                if (tmp.Rows.Count < 1)
                {
                    InsertData($"insert into [DOM_BitRunFeatures]([FeatureName]) values ('{featureTag}')");
                    sql = $"select [BitRunFeatureID] from [DOM_BitRunFeatures] where [FeatureName]='{featureTag}'";
                    tmp = FromDB(sql);
                }
                int MyFeatureId = Convert.ToInt32(tmp.Rows[0][0].ToString());
                long bitNo = Convert.ToInt64(dt1.Rows[i]["BitRunID"].ToString());
                dictBitRunID.TryGetValue(bitNo, out long newBitRun);

                sql = $"insert into [LOG_JobBitRunFeatures]([BitRunID],[BitRunFeatureID],[BitRunFeatureVal]) values({newBitRun},{MyFeatureId},'{dt1.Rows[i]["BitRunFeatureVal"]}')";
                ret = InsertData(sql);
                if (ret != 1) return false;
            }

            System.Diagnostics.Debug.WriteLine("Insert BitRunFeatures");

            //检查补充 TagPool
            var dt2 = ExToTab("TagPool$", excelfile);
            Dictionary<int, int> dictPool = new Dictionary<int, int>();
            int TvdTagId = 28, VStagId = 126;
            foreach (DataRow item in dt2.Rows)
            {
                string Tag = item["TagName"].ToString();
                int oldTagId = Convert.ToInt32(item["TagID"].ToString());
                sql = $"select * from [DOM_TagPool] where [TagName]='{Tag}'";
                var temp = FromDB(sql);
                if (temp.Rows.Count < 1)
                {
                    InsertData($"insert into [DOM_TagPool]([TagName],[Descr]) values ('{Tag}','{item["Descr"]}')");
                    temp = FromDB($"select * from [DOM_TagPool] where [TagName]='{Tag}'");
                    System.Diagnostics.Debug.WriteLine(Tag);
                }
                int newTagID = (int)temp.Rows[0]["TagID"];
                if (Tag == "TVD")
                    TvdTagId = newTagID;
                if (Tag == "VS")
                    VStagId = newTagID;

                dictPool.Add(oldTagId, newTagID);
            }

            var dtSet = ExToTab("ResultsSet$", excelfile);
            System.Diagnostics.Debug.WriteLine("Read Result Sets");

            Dictionary<long, long> dictResltSetID = new Dictionary<long, long>();

            var dtValues = ExToTab("ResultsValues$", excelfile);
            System.Diagnostics.Debug.WriteLine("Read Result Values\nInsert ResultsSet Start...");

            long lastSetID = 0;

            SqlDataAdapter dataAdapter = null;
            DataTable upTable = new DataTable();
            string vals;
            for (int i = 0; i < dtSet.Rows.Count; i++)
            {
                long oldId = Convert.ToInt64(dtSet.Rows[i]["ResultsSetID"].ToString());
                long oldBitId = Convert.ToInt64(dtSet.Rows[i]["BitRunID"].ToString());

                dictBitRunID.TryGetValue(oldBitId, out long bitId);

                float bitdepth = Convert.ToSingle(dtSet.Rows[i]["BitDepthValue"].ToString());
                float measdepth = Convert.ToSingle(dtSet.Rows[i]["MeasDepthValue"].ToString());
                float tvd = Convert.ToSingle(dtSet.Rows[i]["TVD"].ToString());
                int offbotm = Convert.ToInt32(dtSet.Rows[i]["OffBottom"].ToString());
                int annot = Convert.ToInt32(dtSet.Rows[i]["Annotation"].ToString());
                long timesec = Convert.ToInt64(dtSet.Rows[i]["TimeSec"].ToString());

                int granu = Convert.ToInt32(dtSet.Rows[i]["GranularityFlag"].ToString());
                int mannually = Convert.ToInt32(dtSet.Rows[i]["ManuallyAdded"].ToString());
                int tvddone = Convert.ToInt32(dtSet.Rows[i]["TVDDone"].ToString());

                float avcf = NullConvert(dtSet.Rows[i]["AvCF"].ToString());
                float avqf = NullConvert(dtSet.Rows[i]["AvQF"].ToString());
                float pdcd = NullConvert(dtSet.Rows[i]["PDcd"].ToString());
                float wdcf = NullConvert(dtSet.Rows[i]["WdCF"].ToString());
                float wdqf = NullConvert(dtSet.Rows[i]["WdQF"].ToString());
                float wdpa = NullConvert(dtSet.Rows[i]["WdPA"].ToString());


                if (i % 10000 == 0)
                {
                    System.Diagnostics.Debug.WriteLine(i);
                }

                if (i == 0)
                {
                    vals = $"{bitId},{bitdepth},{measdepth},{tvd},{offbotm},{annot},'{dtSet.Rows[i]["Comment"]}','{dtSet.Rows[i]["TDate"]}',{timesec}," +
    $"{granu},{mannually},{tvddone},{avcf},{avqf},{pdcd},{wdcf},{wdqf},{wdpa}";

                    InsertData($"insert into [LOG_ResultsSet]([BitRunID],[BitDepthValue],[MeasDepthValue],[TVD],[OffBottom],[Annotation],[Comment]," +
                        $"[TDate],[TimeSec],[GranularityFlag],[ManuallyAdded],[TVDDone],[AvCF],[AvQF],[PDcd],[WdCF],[WdQF],[WdPA]) values ({vals})");

                    var dtsetTemp = FromDB("select top 1 ResultsSetID from [LOG_ResultsSet] order by ResultsSetID desc");
                    lastSetID = Convert.ToInt64(dtsetTemp.Rows[0]["ResultsSetID"].ToString());

                    dataAdapter = new SqlDataAdapter("select [BitRunID],[BitDepthValue],[MeasDepthValue],[TVD],[OffBottom],[Annotation],[Comment],[TDate]," +
                        "[TimeSec],[GranularityFlag],[ManuallyAdded],[TVDDone],[AvCF],[AvQF],[PDcd],[WdCF],[WdQF],[WdPA] from [LOG_ResultsSet]", Connection);
                    dataAdapter.Fill(upTable);

                }
                else
                {
                    DataRow row = upTable.NewRow();
                    row["BitRunID"] = bitId;
                    row["BitDepthValue"] = bitdepth;
                    row["MeasDepthValue"] = measdepth;
                    row["TVD"] = tvd;
                    row["OffBottom"] = offbotm;
                    row["Annotation"] = annot;

                    row["Comment"] = dtSet.Rows[i]["Comment"];
                    row["TDate"] = dtSet.Rows[i]["TDate"];
                    row["TimeSec"] = timesec;
                    row["GranularityFlag"] = granu;
                    row["ManuallyAdded"] = mannually;
                    row["TVDDone"] = tvddone;

                    row["AvCF"] = avcf;
                    row["AvQF"] = avqf;
                    row["PDcd"] = pdcd;
                    row["WdCF"] = wdcf;
                    row["WdQF"] = wdqf;
                    row["WdPA"] = wdpa;

                    upTable.Rows.Add(row);
                }
                dictResltSetID.Add(oldId, lastSetID);

                lastSetID++;
            }
            SqlCommand insertCommand;

            vals = "";
            foreach (DataColumn c in upTable.Columns)
            {
                if (c.Caption != "ResultsSetID")
                    vals += "@" + c.Caption + ",";
            }
            string sqlinsert = $"insert into [LOG_ResultsSet]([BitRunID],[BitDepthValue],[MeasDepthValue],[TVD],[OffBottom],[Annotation],[Comment]," +
                $"[TDate],[TimeSec],[GranularityFlag],[ManuallyAdded],[TVDDone],[AvCF],[AvQF],[PDcd],[WdCF],[WdQF],[WdPA]) values ({vals.Substring(0, vals.Length - 1)})";

            insertCommand = new SqlCommand(sqlinsert, Connection);

            foreach (DataColumn c in upTable.Columns)
            {
                if (c.Caption != "ResultsSetID")
                    insertCommand.Parameters.Add("@" + c.Caption, ToDbType(c.DataType), 0, c.Caption);
            }

            dataAdapter.InsertCommand = insertCommand;

            int kk = dataAdapter.Update(upTable);
            dataAdapter.Dispose();

            System.Diagnostics.Debug.WriteLine($"ResultSet Completed {kk} Rows.\nInsert ResultValues Start...");

            Dictionary<long, long> dictResValueForSvyRecID = new Dictionary<long, long>();

            upTable = new DataTable();
            dataAdapter = new SqlDataAdapter("select [ResultSetID],[OffBottom],[OffBottomOriginal],[HoleDepth],[TagID],[ValueNum],[ValueOriginal],[YNChar],[Bad],[TDate],[TimeSec] from [LOG_ResultsValues]", Connection);
            dataAdapter.Fill(upTable);

            for (int i = 0; i < dtValues.Rows.Count; i++)
            {
                long oldSetId = Convert.ToInt64(dtValues.Rows[i]["ResultSetID"].ToString());
                long oldRecId = Convert.ToInt64(dtValues.Rows[i]["ResultRecID"].ToString());
                dictResltSetID.TryGetValue(oldSetId, out long newSetId);
                //long newSetId ;

                float HoleDepth = Convert.ToSingle(dtValues.Rows[i]["HoleDepth"].ToString());
                int offbotm = Convert.ToInt32(dtValues.Rows[i]["OffBottom"].ToString());
                int offbotmoriginal = Convert.ToInt32(dtValues.Rows[i]["OffBottomOriginal"].ToString());
                long timesec = Convert.ToInt64(dtValues.Rows[i]["TimeSec"].ToString());

                int OldID = Convert.ToInt32(dtValues.Rows[i]["TagID"].ToString());
                int TagID;
                dictPool.TryGetValue(OldID, out TagID);
                int YNChar = Convert.ToInt32(dtValues.Rows[i]["YNChar"].ToString());
                int bad = Convert.ToInt32(dtValues.Rows[i]["Bad"].ToString());

                DataRow row = upTable.NewRow();
                row["ResultSetID"] = newSetId;
                row["OffBottom"] = offbotm;
                row["OffBottomOriginal"] = offbotmoriginal;
                row["HoleDepth"] = HoleDepth;
                row["TagID"] = TagID;

                row["ValueNum"] = dtValues.Rows[i]["ValueNum"];
                row["ValueOriginal"] = dtValues.Rows[i]["ValueOriginal"];
                row["YNChar"] = YNChar;
                row["Bad"] = bad;
                row["TDate"] = dtValues.Rows[i]["TDate"];
                row["TimeSec"] = timesec;

                upTable.Rows.Add(row);

                /*
                string vals = $"{newSetId},{offbotm},{offbotmoriginal},{HoleDepth},{TagID},'{dtValues.Rows[i]["ValueNum"]}'," +
                    $"'{dtValues.Rows[i]["ValueOriginal"]}',{YNChar},{bad},'{dtValues.Rows[i]["TDate"]}',{timesec}";

                InsertData($"insert into [LOG_ResultsValues]([ResultSetID],[OffBottom],[OffBottomOriginal],[HoleDepth],[TagID],[ValueNum],[ValueOriginal],[YNChar],[Bad],[TDate],[TimeSec]) values ({vals})");

                if (TagID == TvdTagId || TagID == VStagId)
                {
                    var dtsetTemp = FromDB("select top 1 ResultRecID from [LOG_ResultsValues] order by ResultRecID desc");
                    long RecID = Convert.ToInt64(dtsetTemp.Rows[0]["ResultRecID"].ToString());
                    dictResValueForSvyRecID.Add(oldRecId, RecID);
                }
                */

                if (i % 10000 == 0)
                {
                    System.Diagnostics.Debug.WriteLine(i);
                }
            }

            vals = "";
            foreach (DataColumn c in upTable.Columns)
            {
                if (c.Caption != "ResultRecID")
                    vals += "@" + c.Caption + ",";
            }
            sqlinsert = $"insert into [LOG_ResultsValues]([ResultSetID],[OffBottom],[OffBottomOriginal],[HoleDepth],[TagID],[ValueNum],[ValueOriginal],[YNChar],[Bad],[TDate],[TimeSec]) values ({vals.Substring(0, vals.Length - 1)})";

            insertCommand = new SqlCommand(sqlinsert, Connection);
            insertCommand.Parameters.Clear();
            foreach (DataColumn c in upTable.Columns)
            {
                if (c.Caption != "ResultRecID")
                    insertCommand.Parameters.Add("@" + c.Caption, ToDbType(c.DataType), 0, c.Caption);
            }

            dataAdapter.InsertCommand = insertCommand;

            kk = dataAdapter.Update(upTable);
            dataAdapter.Dispose();
            System.Diagnostics.Debug.WriteLine($"ResultValues Completed {kk} Rows!");

            Dictionary<long, long> dictSurveyRecID = new Dictionary<long, long>();
            long lastSvyID = 0;
            var dtSvys = ExToTab("SurveyAnalysis$", excelfile);

            System.Diagnostics.Debug.WriteLine("Read Survey\nInsert SurveyAnalysis Start...");
            long newPrevID = 0;
            for (int i = 0; i < dtSvys.Rows.Count; i++)
            {
                long RecId = Convert.ToInt64(dtSvys.Rows[i]["SvyRecID"].ToString());

                long PrevRecId = Convert.ToInt64(dtSvys.Rows[i]["SvyRecIDPrev"].ToString());

                float mDepth = Convert.ToSingle(dtSvys.Rows[i]["MDepth"].ToString());
                float Inc = Convert.ToSingle(dtSvys.Rows[i]["Inc"].ToString());
                float Azm = Convert.ToSingle(dtSvys.Rows[i]["Azm"].ToString());
                float MagF = Convert.ToSingle(dtSvys.Rows[i]["MagF"].ToString());
                float Dip = Convert.ToSingle(dtSvys.Rows[i]["Dip"].ToString());

                float GTot = Convert.ToSingle(dtSvys.Rows[i]["GTot"].ToString());
                float X = Convert.ToSingle(dtSvys.Rows[i]["X"].ToString());
                float Y = Convert.ToSingle(dtSvys.Rows[i]["Y"].ToString());
                float Z = Convert.ToSingle(dtSvys.Rows[i]["Z"].ToString());
                float VertSection = Convert.ToSingle(dtSvys.Rows[i]["VertSection"].ToString());

                float DogLeg = Convert.ToSingle(dtSvys.Rows[i]["DogLeg"].ToString());
                float AvPA = NullConvert(dtSvys.Rows[i]["AvPA"].ToString());
                float ResA = NullConvert(dtSvys.Rows[i]["ResA"].ToString());
                float ResB = NullConvert(dtSvys.Rows[i]["ResB"].ToString());
                float ResV = NullConvert(dtSvys.Rows[i]["ResV"].ToString());
                float MDepthRes = NullConvert(dtSvys.Rows[i]["MDepthRes"].ToString());

                int SvyBad = Convert.ToInt32(dtSvys.Rows[i]["SvyBad"].ToString());
                int SurfacePt = Convert.ToInt32(dtSvys.Rows[i]["SurfacePt"].ToString());

                long OldBitRunId = Convert.ToInt64(dtSvys.Rows[i]["BitRunID"].ToString());
                long oldResSetId = Convert.ToInt64(dtSvys.Rows[i]["ResultsSetID"].ToString());

                int mannually = Convert.ToInt32(dtSvys.Rows[i]["ManuallyAdded"].ToString());

                long oldTVDResultValueID = Convert.ToInt64(dtSvys.Rows[i]["TVDResultValueID"].ToString());
                long oldVSResultValueID = Convert.ToInt64(dtSvys.Rows[i]["VSResultValueID"].ToString());
                long timesec = Convert.ToInt64(dtSvys.Rows[i]["TimeSec"].ToString());

                // BitRunID
                dictBitRunID.TryGetValue(OldBitRunId, out long BitRunId);

                // TVD VS rec ID
                //dictResValueForSvyRecID.TryGetValue(oldTVDResultValueID, out long TVDResultValueID);
                //dictResValueForSvyRecID.TryGetValue(oldVSResultValueID, out long VSResultValueID);
                long TVDResultValueID = 0, VSResultValueID = 0;
                long ResSetId = 0;
                if (oldResSetId > 0)
                {
                    // ResultsSetID
                    dictResltSetID.TryGetValue(oldResSetId, out ResSetId);
                    var temp = FromDB($"select ResultRecID from [LOG_ResultsValues] where ResultSetID={ResSetId} and TagID={TvdTagId}");
                    if (temp.Rows.Count > 0)
                        TVDResultValueID = (long)temp.Rows[0][0];
                    temp = FromDB($"select ResultRecID from [LOG_ResultsValues] where ResultSetID={ResSetId} and TagID={VStagId}");
                    if (temp.Rows.Count > 0)
                        TVDResultValueID = (long)temp.Rows[0][0];
                }

                // PrevRecId
                if (PrevRecId > 0)
                {
                    dictResValueForSvyRecID.TryGetValue(PrevRecId, out newPrevID);
                }

                vals = $"{newPrevID},'{dtSvys.Rows[i]["StationNo"]}',{mDepth},{Inc},{Azm},{MagF},{Dip},{GTot},{X},{Y},{Z},{VertSection},{SvyBad}," +
                 $"'{dtSvys.Rows[i]["TDate"]}',{BitRunId},{DogLeg},{mannually},{ResSetId},{timesec},{SurfacePt},{TVDResultValueID},{VSResultValueID},{AvPA},{ResA},{ResB},{ResV},{MDepthRes}";

                InsertData($"insert into [LOG_SurveyAnalysis]([SvyRecIDPrev],[StationNo],[MDepth],[Inc],[Azm],[MagF],[Dip],[GTot],[X],[Y],[Z]," +
                    $"[VertSection],[SvyBad],[TDate],[BitRunID],[DogLeg],[ManuallyAdded],[ResultsSetID],[TimeSec],[SurfacePt],[TVDResultValueID]," +
                    $"[VSResultValueID],[AvPA],[ResA],[ResB],[ResV],[MDepthRes]) values ({vals})");

                if (i == 0)
                {
                    var dtsetTemp = FromDB("select top 1 SvyRecID from [LOG_SurveyAnalysis] order by SvyRecID desc");
                    lastSvyID = Convert.ToInt64(dtsetTemp.Rows[0]["SvyRecID"].ToString());
                }

                dictSurveyRecID.Add(RecId, lastSvyID);
                lastSvyID++;
            }

            System.Diagnostics.Debug.WriteLine("SurveyAnalysis Completed!");

            Connection.Close();

            return true;
        }

        public void DeleteJob()
        {
            string sql;

            if (TotalRuns > 0)
            {
                //BitRunFeatures
                sql = $"delete from LOG_JobBitRunFeatures where BitRunID in ({strBitRuns})";
                int cnt = SQLCommand(sql);
                System.Diagnostics.Debug.WriteLine($"Delete {cnt} BitRunFeatures.");

                //SurveyAnalysis
                sql = $"delete from LOG_SurveyAnalysis where [BitRunID] in ({strBitRuns})";
                cnt = SQLCommand(sql);
                System.Diagnostics.Debug.WriteLine($"Delete {cnt} SurveyAnalysis.");

                //ResultValues
                sql = $"delete LOG_ResultsValues from LOG_ResultsValues inner join LOG_ResultsSet on LOG_ResultsValues.ResultSetID = LOG_ResultsSet.ResultsSetID and LOG_ResultsSet.BitRunID in ({strBitRuns})";
                cnt = SQLCommand(sql);
                System.Diagnostics.Debug.WriteLine($"Delete {cnt} ResultsValues.");

                //ResultSet
                sql = $"delete from LOG_ResultsSet where [BitRunID] in ({strBitRuns})";
                cnt = SQLCommand(sql);
                System.Diagnostics.Debug.WriteLine($"Delete {cnt} ResultsSet.");

                //leg BitRuns
                sql = $"delete from LOG_JobLegBitRuns where BitRunID in ({strBitRuns})";
                cnt = SQLCommand(sql);
                System.Diagnostics.Debug.WriteLine($"Delete {cnt} BitRuns.");

            }

            sql = $"delete from LOG_JobLegs where JobID = {jobID}";
            int kk = SQLCommand(sql);
            System.Diagnostics.Debug.WriteLine($"Delete {kk} Legs.");

            //job hdrParams
            sql = $"delete from LOG_JobHdrParams where JobID = {jobID}";
            kk = SQLCommand(sql);
            System.Diagnostics.Debug.WriteLine($"Delete {kk} JobHdrParams.");
            sql = $"delete from LOG_Job where JobID = {jobID}";
            kk = SQLCommand(sql);
            System.Diagnostics.Debug.WriteLine($"Delete {kk} Job.");

            System.Diagnostics.Debug.WriteLine($"Delete Job {jobID} Completed.");

        }

        private float NullConvert(string x)
        {
            if (string.IsNullOrEmpty(x))
            {
                return -9999;
            }
            else
            {
                return Convert.ToSingle(x);
            }
        }

        private SqlDbType ToDbType(Type i)
        {
            SqlDbType s;
            switch (i.Name)
            {
                case "String":
                    s = SqlDbType.Char;
                    break;
                case "Int32":
                    s = SqlDbType.Int;
                    break;
                case "Int64":
                    s = SqlDbType.BigInt;
                    break;
                case "Int16":
                    s = SqlDbType.TinyInt;
                    break;
                case "Double":
                    s = SqlDbType.Decimal;
                    break;
                case "Single":
                    s = SqlDbType.Real;
                    break;
                case "Decimal":
                    s = SqlDbType.Decimal;
                    break;
                default:
                    s = SqlDbType.Char;
                    break;
            }
            return s;
        }

        private int SQLCommand(string insert)
        {
            SqlConnection Connection = new SqlConnection(sqlserverConnStr);
            SqlCommand CommandSql;
            Connection.Open();
            int k;
            CommandSql = new SqlCommand(insert, Connection);
            k = CommandSql.ExecuteNonQuery();
            Connection.Close();
            return k;
        }

        private DataTable FromDB(string sql)
        {
            SqlConnection Connection = new SqlConnection(sqlserverConnStr);
            SqlCommand CommandSql;
            Connection.Open();

            SqlDataAdapter dataadpt;

            CommandSql = new SqlCommand(sql, Connection);
            CommandSql.CommandTimeout = 60;
            dataadpt = new SqlDataAdapter(CommandSql);

            DataTable dt = new DataTable();

            dataadpt.Fill(dt);
            dataadpt.Dispose();
            CommandSql.Dispose();
            Connection.Close();
            return dt;
        }

        private SqlConnection Connection;

        private int InsertData(string insert)
        {
            //SqlConnection Connection = new SqlConnection(sqlserverConnStr);
            SqlCommand CommandSql;
            //Connection.Open();
            int k;
            CommandSql = new SqlCommand(insert, Connection);
            k = CommandSql.ExecuteNonQuery();
            //Connection.Close();
            return k;
        }

        private DataTable ExToTab(string SheetName, string ExcelPath)
        {
            string strConn = "Provider=Microsoft.ACE.OLEDB.12.0; Data Source=" + ExcelPath + "; Extended Properties=Excel 12.0;";
            OleDbConnection Conn = new OleDbConnection(strConn);
            try
            {
                Conn.Open();
            }
            catch (Exception)
            {
                Console.WriteLine();
                Console.Write("您安装的Excel功能不全（缺少数据库引擎功能），请先安装AccessDatabaseEngine!", "温馨提示");
                return null;
            }

            string cmdsql;
            DataTable dt = new DataTable();
            dt.Reset();

            cmdsql = "select * from [" + SheetName + "]";
            OleDbCommand custCMD = new OleDbCommand(cmdsql, Conn);
            OleDbDataAdapter dtad = new OleDbDataAdapter(custCMD);
            try
            {
                dtad.Fill(dt);
            }
            catch (OleDbException)
            {
                Console.WriteLine();
                Console.Write("数据导入失败，请检查Excel格式");
                custCMD.Dispose();
                dtad.Dispose();
                Conn.Dispose();
                return null;
            }

            custCMD.Dispose();
            dtad.Dispose();
            Conn.Dispose();

            return dt;
        }

    }

    public class BitRun
    {
        public long Id;
        public int No;
        public long LegId;
        public string LegName;

        public double SurveyToBit;
        public double GammaToBit;

        public List<GR_feature> features;

    }

    public class GR_feature
    {
        public string FeatureName;
        public double GRScale;
        public double GROffset;
    }

    public class MezDataSet
    {
        public List<surveyPoint> SvyList;       
        public GrData[] GrList;
    }

    public class GrData
    {
        public string GrName;
        public List<tagDataPoint> grList;
    }

    public class tagDataPoint
    {
        public double depth;
        public double val;
    }

    public class surveyPoint
    {
        public double MDepth;
        public double Inc;
        public double Azm;
        public double TVD;

    }

    public class TransItem
    {
        public string GvName;
        public double Depth;
        public double measDepth;
        public double RetVal;
        public string tDate;
    }
    public class TransData
    {
        public List<TransItem> Surveys;
        public List<TransItem> GVDatas;
        public bool HasSvy { get; set; }
        public bool HasGv { get; set; }
    }
}
