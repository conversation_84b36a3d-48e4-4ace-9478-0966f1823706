﻿<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="TartanLogApp.MainWindow"
        Title="达坦实时Gamma成像系统 - 最小化验证版本" Height="600" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="40"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="LightBlue" Padding="10">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="🎯 达坦实时Gamma成像系统" FontSize="20" FontWeight="Bold" VerticalAlignment="Center"/>
                <TextBlock Text="(Avalonia UI 验证版本)" FontSize="14" Margin="20,0,0,0" VerticalAlignment="Center" Foreground="DarkBlue"/>
            </StackPanel>
        </Border>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧控制面板 -->
            <StackPanel Grid.Column="0" Spacing="15">
                <TextBlock Text="系统控制" FontSize="16" FontWeight="Bold"/>

                <StackPanel Orientation="Horizontal" Spacing="10">
                    <TextBlock Text="JobID:" VerticalAlignment="Center" Width="60"/>
                    <TextBox x:Name="tbJobID" Width="100" Height="30"/>
                    <Button x:Name="btnSaveJob" Content="保存" Width="60" Height="30" Click="btnSaveJob_Click"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" Spacing="10">
                    <TextBlock Text="井号:" VerticalAlignment="Center" Width="60"/>
                    <TextBox x:Name="tbWellNm" Width="150" Height="30"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" Spacing="10">
                    <Button x:Name="btnJobConnect" Content="连接" Width="80" Height="35" Click="btnJobConnect_Click"/>
                    <Button x:Name="btnJobDisConnect" Content="断开" Width="80" Height="35" Click="btnJobDisConnect_Click"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" Spacing="10">
                    <Button x:Name="btnDataTrans" Content="数据远传" Width="100" Height="35" Click="btnDataTrans_Click"/>
                    <Button x:Name="btnNetListen" Content="网络接收" Width="100" Height="35" Click="btnNetListen_Click"/>
                </StackPanel>
            </StackPanel>

            <!-- 右侧状态显示 -->
            <StackPanel Grid.Column="1" Spacing="15">
                <TextBlock Text="系统状态" FontSize="16" FontWeight="Bold"/>

                <Border Background="LightGray" Padding="10" CornerRadius="5">
                    <StackPanel Spacing="5">
                        <TextBlock Text="连接状态:" FontWeight="Bold"/>
                        <Ellipse x:Name="lblStatus" Height="20" Width="20" Fill="Red" HorizontalAlignment="Left"/>
                        <TextBlock x:Name="tbStatusText" Text="未连接" FontSize="12"/>
                    </StackPanel>
                </Border>

                <Border Background="LightYellow" Padding="10" CornerRadius="5">
                    <StackPanel Spacing="5">
                        <TextBlock Text="测试按钮:" FontWeight="Bold"/>
                        <Button x:Name="btnTest" Content="测试核心功能" Width="150" Height="35" Click="btnTest_Click"/>
                        <TextBlock x:Name="tbTestResult" Text="等待测试..." FontSize="12" Foreground="Blue"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="DarkGray" Padding="10">
            <TextBlock x:Name="tbStatusBar" Text="系统就绪 - Avalonia UI 迁移验证版本" Foreground="White"/>
        </Border>
    </Grid>
</Window>
