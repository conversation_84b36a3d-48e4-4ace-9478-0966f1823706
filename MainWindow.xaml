﻿<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="TartanLogApp.MainWindow"
        Title="达坦实时Gamma成像系统 - 现代化版本" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="70"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>

        <!-- 顶部标题栏 -->
        <Border Grid.Row="0" Background="#2196F3">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo和标题 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="White" CornerRadius="15" Width="30" Height="30" Margin="0,0,10,0">
                        <TextBlock Text="🎯" FontSize="18" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="达坦实时Gamma成像系统" FontSize="20" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="Tartan Real-time Gamma Imaging System" FontSize="10" Foreground="White" Opacity="0.8"/>
                    </StackPanel>
                </StackPanel>

                <!-- 版本信息 -->
                <StackPanel Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Right">
                    <TextBlock Text="Avalonia UI" FontSize="12" FontWeight="Medium" Foreground="White" HorizontalAlignment="Right"/>
                    <TextBlock Text="V1.0.0.17" FontSize="10" Foreground="White" Opacity="0.8" HorizontalAlignment="Right"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1" Background="#F5F5F5" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="250"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧控制面板 -->
            <Border Grid.Column="0" Background="White" CornerRadius="5" Margin="5" Padding="15">
                <StackPanel Spacing="15">

                    <!-- Job设置 -->
                    <StackPanel Spacing="10">
                        <TextBlock Text="📋 Job 设置" FontSize="16" FontWeight="Bold" Foreground="#1976D2"/>

                        <StackPanel Spacing="8">
                            <TextBlock Text="JobID" FontSize="12" FontWeight="Medium"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox x:Name="tbJobID" Grid.Column="0" Text="2514" Margin="0,0,5,0" Height="28"/>
                                <Button x:Name="btnSaveJob" Grid.Column="1" Content="导出" Click="btnSaveJob_Click" Margin="0,0,5,0" Height="28" Width="50" Background="#FF9800" Foreground="White"/>
                                <Ellipse x:Name="lblStatus" Grid.Column="2" Height="10" Width="10" Fill="Red" VerticalAlignment="Center"/>
                            </Grid>
                        </StackPanel>

                        <StackPanel Spacing="8">
                            <TextBlock Text="井号" FontSize="12" FontWeight="Medium"/>
                            <TextBox x:Name="tbWellNm" Height="28" Watermark="请输入井号"/>
                        </StackPanel>

                        <Separator Background="#E0E0E0" Height="1" Margin="0,5"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Button x:Name="btnJobConnect" Grid.Column="0" Content="🔗 连接" Click="btnJobConnect_Click" Margin="0,0,3,0" Height="32" Background="#2196F3" Foreground="White"/>
                            <Button x:Name="btnJobDisConnect" Grid.Column="1" Content="🔌 断开" Click="btnJobDisConnect_Click" Margin="3,0,0,0" Height="32" Background="#FF9800" Foreground="White"/>
                        </Grid>
                    </StackPanel>

                    <!-- 网络功能 -->
                    <StackPanel Spacing="10">
                        <TextBlock Text="🌐 网络功能" FontSize="16" FontWeight="Bold" Foreground="#1976D2"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Button x:Name="btnDataTrans" Grid.Column="0" Content="📤 数据远传" Click="btnDataTrans_Click" Margin="0,0,3,0" Height="32" Background="#2196F3" Foreground="White"/>
                            <Button x:Name="btnNetListen" Grid.Column="1" Content="📥 网络接收" Click="btnNetListen_Click" Margin="3,0,0,0" Height="32" Background="#4CAF50" Foreground="White"/>
                        </Grid>
                    </StackPanel>

                </StackPanel>
            </Border>

            <!-- 中间图表区域 -->
            <Border Grid.Column="1" Background="White" CornerRadius="5" Margin="5" Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 图表标题 -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,10">
                        <TextBlock Text="📈 实时Gamma曲线" FontSize="18" FontWeight="Bold" Foreground="#1976D2"/>
                        <TextBlock Text="Real-time Gamma Curve Display" FontSize="10" Foreground="#666"/>
                    </StackPanel>

                    <!-- 图表显示区域 -->
                    <Border Grid.Row="1" Background="#E3F2FD" CornerRadius="3">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="8">
                            <TextBlock Text="📊" FontSize="40" HorizontalAlignment="Center" Opacity="0.6"/>
                            <TextBlock Text="Gamma曲线图表区域" FontSize="14" FontWeight="Medium" HorizontalAlignment="Center" Opacity="0.8"/>
                            <TextBlock Text="Chart Area - Ready for Data" FontSize="10" HorizontalAlignment="Center" Opacity="0.6"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>

            <!-- 右侧状态面板 -->
            <Border Grid.Column="2" Background="White" CornerRadius="5" Margin="5" Padding="15">
                <StackPanel Spacing="15">

                    <!-- 系统状态 -->
                    <StackPanel Spacing="10">
                        <TextBlock Text="📊 系统状态" FontSize="16" FontWeight="Bold" Foreground="#1976D2"/>

                        <!-- 连接状态 -->
                        <Border Background="#F5F5F5" CornerRadius="3" Padding="8">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="5" VerticalAlignment="Center">
                                    <Ellipse Height="12" Width="12" Fill="Red"/>
                                    <TextBlock Text="连接状态:" FontSize="11" FontWeight="Medium"/>
                                </StackPanel>

                                <TextBlock x:Name="tbStatusText" Grid.Column="1" Text="未连接" FontSize="10" VerticalAlignment="Center" HorizontalAlignment="Right"/>
                            </Grid>
                        </Border>

                        <!-- 测试功能 -->
                        <StackPanel Spacing="8">
                            <Button x:Name="btnTest" Content="🔧 测试核心功能" Click="btnTest_Click" Height="32" Background="#2196F3" Foreground="White"/>
                            <Border Background="#F5F5F5" CornerRadius="3" Padding="8">
                                <TextBlock x:Name="tbTestResult" Text="等待测试..." FontSize="10" TextWrapping="Wrap"/>
                            </Border>
                        </StackPanel>
                    </StackPanel>

                </StackPanel>
            </Border>
        </Grid>

        <!-- 底部状态栏 -->
        <Border Grid.Row="2" Background="#37474F">
            <Grid Margin="10,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="tbStatusBar"
                           Grid.Column="0"
                           Text="系统就绪 - Avalonia UI 现代化版本"
                           Foreground="White"
                           VerticalAlignment="Center"
                           FontSize="11"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10" VerticalAlignment="Center">
                    <TextBlock Text="🕒" Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock x:Name="tbCurrentTime" Text="2024-08-01 12:00:00" Foreground="White" FontSize="11" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
